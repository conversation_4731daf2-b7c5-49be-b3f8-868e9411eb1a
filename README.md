# iad-pricing

## Description

This service isn't meant for mapping any entity, instead it's meant to provide the quote functionality to all the products.
By the writing time, it will just handle three cases:
- Going to pg for company's quotation;
- Going to product for internal quotation.
- Going to Backoffice for price by coupon (discount to an order price with a couponCode).

Its business logic is common to all the tenants.
This service will be deployed not only in the common namespace, but also in some tenants, such as FCA and ITAS.

## Exposed Path

- POST `/v2/quote`
- PUT `/v1/price/coupon/{couponCode}`
- PUT `/v1/quote`

## External Communication

This ms communicates with:

- providers-gateway ms;
- product ms;
- adp ms;
- backoffice ms;
- customer ms;
- token ms;
- order ms;

## Entity

This ms doesn't handle any entity.

### quote V2

- POST `/v2/quote`

**description**: this endpoint calls Iam (iad-token) and retrieves a token, and then calls product ms, order ms and customer ms (find by id), and returns the respective responses; these responses will be used for building an object that will be passed to the quotator, which can be EXTERNAL or INTERNAL, depending on the quotation type specified in the product response's configuration. in the response object, returned by the /v2/quote endpoint, there will be contained two fields: data (the quote returned by the quotator) and dataOrder (the updated quotation by order code, by the order ms).

Request JSON sample:
```
{
    "data": {
        "productId": 1,
        "customerId": 155,
        "orderId": 9405
    }
}
```

### coupon

- PUT `/v1/price/coupon/{couponCode}`

- Applies discount to an order price with a couponCode

**Note**: the discount will be applied only if the couponCode product(in the ruleId of the promotion) is the same of the product item

Request JSON sample:

```json
{
  "data": {
    "order": {
      "id": 6452,
      "orderCode": "Y-4bc0432",
      "orderItem": {
        "id": 6436
      }
    }
  }
}
```

### quote V1

- PUT `/v1/quote`

**description**: this endpoint, first of all, calls the adp (POST /pg/quote/toPg), which returns a transfomed JSON that it will be sent to the PG ms (POST /providers/quote), and then the quotation JSON object returned by the PG is transformed again by another call to the adp (POST /pg/quote/fromPg), and then returned by the endpoint. the JSON of the request may vary from tenant to tenant.

Request JSON sample, this is an example for tim-my-home on TIM Customer DEV: curl --location --request PUT 'https://timcustomer-api.dev.yoloassicurazioni.it/iad-pricing/v1/quote' \
--header 'Content-Type: application/json' \
--data '
```
{
    "product_code": "tim-my-home",
    "addons": [
        {
            "code": "A",
            "maximal": 400
        }
    ]
}'
```

## Swagger URL

By clicking on this link, you can get to the swagger documentation to be able to use the microservice endpoints:

http://tenant-api.yoloassicurazioni.it/iad-pricing/openapi/swagger