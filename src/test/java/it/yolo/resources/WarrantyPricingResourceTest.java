package it.yolo.resources;

import io.quarkus.test.junit.QuarkusTest;
import io.restassured.http.ContentType;
import it.yolo.model.BaseData;
import it.yolo.model.OrderQuoteRequest;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;

/**
 * Test class per WarrantyPricingResource
 * 
 * Questi test coprono l'endpoint /order-quote che:
 * 1. Recupera un ordine tramite OrderService
 * 2. Estrae i dettagli delle garanzie dall'ordine
 * 3. Calcola i premi annuali e mensili
 * 4. Aggiorna l'ordine con le quotazioni calcolate
 * 5. Restituisce la quotazione totale annuale
 * 
 * Scenari testati:
 * - Successo con ordini validi (singola e multipla garanzia)
 * - Gestione errori (ordine non trovato, prezzi non trovati, ecc.)
 * - Validazione input (order code vuoto, nullo, mancante)
 * - Casi edge (nessuna garanzia nell'ordine, fallimento aggiornamento ordine)
 * - Errori interni del server
 */
@QuarkusTest
class WarrantyPricingResourceTest {

    // ========================
    // Tests per endpoint /order-quote
    // ========================

    @Test
    void testGetQuoteFromOrder_Success() {
        OrderQuoteRequest request = new OrderQuoteRequest("ORD-12345");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(200)
            .body("data.totalPremium", notNullValue())
            .body("data.premiumCurrency", notNullValue())
            .body("data.benefitCurrency", notNullValue());
    }

    @Test
    void testGetQuoteFromOrder_OrderNotFound() {
        OrderQuoteRequest request = new OrderQuoteRequest("NON-EXISTING-ORDER");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(404)
            .body("data.error", containsString("Order not found"));
    }

    @Test
    void testGetQuoteFromOrder_BlankOrderCode() {
        OrderQuoteRequest request = new OrderQuoteRequest("");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(400);
    }

    @Test
    void testGetQuoteFromOrder_NullOrderCode() {
        OrderQuoteRequest request = new OrderQuoteRequest(null);
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(400);
    }

    @Test
    void testGetQuoteFromOrder_NoWarrantyDetailsInOrder() {
        // Test per un ordine che non contiene dettagli di garanzia
        OrderQuoteRequest request = new OrderQuoteRequest("ORD-NO-WARRANTIES");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(400)
            .body("data.error", containsString("No warranty details found"));
    }

    @Test
    void testGetQuoteFromOrder_PriceNotFound() {
        // Test per un ordine con garanzie per cui non sono disponibili i prezzi
        OrderQuoteRequest request = new OrderQuoteRequest("ORD-NO-PRICES");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(404)
            .body("data.error", containsString("Price not found"));
    }

    @Test
    void testGetQuoteFromOrder_InvalidOrderData() {
        // Test per un ordine con dati malformati
        OrderQuoteRequest request = new OrderQuoteRequest("ORD-INVALID-DATA");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(400)
            .body("data.error", notNullValue());
    }

    @Test
    void testGetQuoteFromOrder_InternalServerError() {
        // Test per simulare un errore interno del server
        OrderQuoteRequest request = new OrderQuoteRequest("ORD-SERVER-ERROR");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(500)
            .body("data.error", equalTo("Internal server error"));
    }

    @Test
    void testGetQuoteFromOrder_SuccessWithMultipleWarranties() {
        // Test per un ordine con multiple garanzie
        OrderQuoteRequest request = new OrderQuoteRequest("ORD-MULTI-WARRANTIES");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(200)
            .body("data.totalPremium", notNullValue())
            .body("data.totalPremium", greaterThan(0f))
            .body("data.premiumCurrency", notNullValue())
            .body("data.benefitCurrency", notNullValue());
    }

    @Test
    void testGetQuoteFromOrder_ValidationError_MissingOrderCode() {
        // Test per validazione con orderCode mancante nel JSON
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(new OrderQuoteRequest());

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(400);
    }

    @Test
    void testGetQuoteFromOrder_UpdateOrderFailure() {
        // Test per il caso in cui l'aggiornamento dell'ordine fallisce ma il calcolo riesce
        OrderQuoteRequest request = new OrderQuoteRequest("ORD-UPDATE-FAIL");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(200) // Dovrebbe comunque restituire 200 anche se l'update fallisce
            .body("data.totalPremium", notNullValue())
            .body("data.premiumCurrency", notNullValue())
            .body("data.benefitCurrency", notNullValue());
    }

    @Test
    void testGetQuoteFromOrder_SuccessWithDifferentDuration() {
        // Test per un ordine con durata diversa da 1 anno
        OrderQuoteRequest request = new OrderQuoteRequest("ORD-3-YEARS");
        BaseData<OrderQuoteRequest> requestData = new BaseData<>(request);

        given()
            .contentType(ContentType.JSON)
            .body(requestData)
        .when()
            .post("/v1/pricing/order-quote")
        .then()
            .statusCode(200)
            .body("data.totalPremium", notNullValue())
            .body("data.premiumCurrency", notNullValue())
            .body("data.benefitCurrency", notNullValue());
    }
}
