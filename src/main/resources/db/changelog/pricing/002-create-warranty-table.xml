<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="002-create-warranty-table" author="RoccoScavone">
        <comment>Create warranty_premiums table for storing versioned premium calculations</comment>

        <createTable tableName="warranty_premiums" schemaName="pricing">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="warranty_id" type="INTEGER">
                <!-- Aggiunta della Foreign Key Constraint inline per chiarezza -->
                <constraints nullable="false"
                             foreignKeyName="fk_warrantypremiums_to_anagwarranties"
                             referencedTableSchemaName="product"
                             referencedTableName="anag_warranties"
                             referencedColumnNames="id"/>
            </column>
            <column name="benefit_amount" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="duration_months" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="total_premium" type="DECIMAL(15,2)">
                <constraints nullable="false"/>
            </column>
            <!-- Ho cambiato il default in PLN per il tenant polacco e usato due colonne per flessibilità -->
            <column name="benefit_currency" type="VARCHAR(3)" defaultValue="PLN">
                <constraints nullable="false"/>
            </column>
            <column name="premium_currency" type="VARCHAR(3)" defaultValue="PLN">
                <constraints nullable="false"/>
            </column>
            <!-- Aggiunta delle colonne per il versionamento/storicità -->
            <column name="valid_from" type="DATE" defaultValueComputed="CURRENT_DATE">
                <constraints nullable="false"/>
            </column>
            <column name="valid_to" type="DATE"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP"/>
        </createTable>

        <!-- Uso di <addUniqueConstraint> per maggiore chiarezza semantica. Include valid_from per il versionamento. -->
        <addUniqueConstraint constraintName="uq_warranty_premiums_versioned"
                             tableName="warranty_premiums"
                             schemaName="pricing"
                             columnNames="warranty_id, benefit_amount, duration_months, valid_from"/>

        <!-- L'indice su warranty_id è ancora utile e corretto -->
        <createIndex indexName="idx_warranty_premiums_warranty_id"
                     tableName="warranty_premiums"
                     schemaName="pricing">
            <column name="warranty_id"/>
        </createIndex>

        <rollback>
            <dropTable tableName="warranty_premiums" schemaName="pricing"/>
        </rollback>
    </changeSet>

</databaseChangeLog>