<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="007-remove-trailing-dot-zero-zero-from-benefit-amount" author="RoccoScavone">
        <comment>
            Rimuove i '.00' finali dai valori di benefit_amount nella tabella warranty_premiums.
            Ad esempio, '100.00' diventa '100'.
            Questo si applica solo ai valori che sono stringhe numeriche terminanti esattamente con '.00'.
        </comment>

        <sql dbms="postgresql">
            UPDATE warranty_premiums
            SET benefit_amount = REGEXP_REPLACE(benefit_amount, E'\\.00$', '')
            WHERE benefit_amount ~ E'^\\d+\\.00$';
        </sql>
    </changeSet>
</databaseChangeLog>