<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="001-create-pricing-schema" author="RoccoScavone">
        <comment>Create pricing schema for warranty premiums</comment>
        
        <sql>
            CREATE SCHEMA IF NOT EXISTS pricing;
        </sql>
        
        <rollback>
            DROP SCHEMA IF EXISTS pricing CASCADE;
        </rollback>
    </changeSet>

</databaseChangeLog>
