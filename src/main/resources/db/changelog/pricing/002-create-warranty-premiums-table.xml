<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="002-create-warranty-premiums-table" author="augment-code">
        <comment>Create warranty_premiums table for storing premium calculations</comment>
        
        <createTable tableName="warranty_premiums" schemaName="pricing">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="warranty_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="benefit_amount" type="DECIMAL(15,2)">
                <constraints nullable="false"/>
            </column>
            <column name="duration_months" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="total_premium" type="DECIMAL(15,2)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(3)" defaultValue="EUR">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <!-- Create unique index for warranty_id, benefit_amount, duration_months combination -->
        <createIndex indexName="idx_warranty_premiums_unique" tableName="warranty_premiums" schemaName="pricing" unique="true">
            <column name="warranty_id"/>
            <column name="benefit_amount"/>
            <column name="duration_months"/>
        </createIndex>
        
        <!-- Create index for faster lookups -->
        <createIndex indexName="idx_warranty_premiums_warranty_id" tableName="warranty_premiums" schemaName="pricing">
            <column name="warranty_id"/>
        </createIndex>
        
        <rollback>
            <dropTable tableName="warranty_premiums" schemaName="pricing"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
