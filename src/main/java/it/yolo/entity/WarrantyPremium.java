package it.yolo.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "warranty_premiums", schema = "pricing")
public class WarrantyPremium {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "warranty_id", nullable = false)
    private Integer warrantyId;

    @Column(name = "benefit_amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal benefitAmount;

    @Column(name = "duration_months", nullable = false)
    private Integer durationMonths;

    @Column(name = "total_premium", nullable = false, precision = 15, scale = 2)
    private BigDecimal totalPremium;

    @Column(name = "currency", nullable = false, length = 3)
    private String currency = "EUR";

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    // Constructors
    public WarrantyPremium() {
        this.createdAt = LocalDateTime.now();
    }

    public WarrantyPremium(Integer warrantyId, BigDecimal benefitAmount, Integer durationMonths, BigDecimal totalPremium) {
        this();
        this.warrantyId = warrantyId;
        this.benefitAmount = benefitAmount;
        this.durationMonths = durationMonths;
        this.totalPremium = totalPremium;
    }

    public WarrantyPremium(Integer warrantyId, BigDecimal benefitAmount, Integer durationMonths, BigDecimal totalPremium, String currency) {
        this(warrantyId, benefitAmount, durationMonths, totalPremium);
        this.currency = currency;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getWarrantyId() {
        return warrantyId;
    }

    public void setWarrantyId(Integer warrantyId) {
        this.warrantyId = warrantyId;
    }

    public BigDecimal getBenefitAmount() {
        return benefitAmount;
    }

    public void setBenefitAmount(BigDecimal benefitAmount) {
        this.benefitAmount = benefitAmount;
    }

    public Integer getDurationMonths() {
        return durationMonths;
    }

    public void setDurationMonths(Integer durationMonths) {
        this.durationMonths = durationMonths;
    }

    public BigDecimal getTotalPremium() {
        return totalPremium;
    }

    public void setTotalPremium(BigDecimal totalPremium) {
        this.totalPremium = totalPremium;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "WarrantyPremium{" +
                "id=" + id +
                ", warrantyId=" + warrantyId +
                ", benefitAmount=" + benefitAmount +
                ", durationMonths=" + durationMonths +
                ", totalPremium=" + totalPremium +
                ", currency='" + currency + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
