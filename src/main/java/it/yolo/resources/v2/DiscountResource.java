package it.yolo.resources.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.exception.OrderException;
import it.yolo.model.DiscountRequest;
import it.yolo.service.v2.StandardDiscountService;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import org.jboss.logging.Logger;

/**
 * Resource for discount and coupon management
 */
@Path("/v2")
public class DiscountResource {

    private static final Logger LOG = Logger.getLogger(DiscountResource.class);

    @Inject
    StandardDiscountService discountService;

    /**
     * Apply a coupon code to an order
     *
     * @param token Authorization token
     * @param request Request containing orderCode and discountCode
     * @return Updated order with applied discount
     */
    @POST
    @Path("/applyCoupon")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public JsonNode applyCouponCode(@HeaderParam("Authorization") String token, DiscountRequest request) throws JsonProcessingException, OrderException {
        LOG.infof("Applying coupon: orderCode=%s, discountCode=%s", request.getOrderCode(), request.getDiscountCode());
        return discountService.standardDiscount(token, request);
    }

    /**
     * Remove a coupon from an order
     *
     * @param token Authorization token
     * @param orderCode Order code
     * @return Updated order without coupon
     */
    @DELETE
    @Path("/deleteCoupon/{orderCode}")
    @Produces(MediaType.APPLICATION_JSON)
    public JsonNode deleteCoupon(@HeaderParam("Authorization") String token, @PathParam("orderCode") String orderCode) throws OrderException {
        LOG.infof("Deleting coupon for orderCode=%s", orderCode);
        return discountService.deleteCoupon(token, orderCode);
    }
}
