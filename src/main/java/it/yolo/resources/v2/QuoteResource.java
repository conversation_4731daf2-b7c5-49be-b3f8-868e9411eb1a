package it.yolo.resources.v2;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.exception.*;
import it.yolo.service.v2.QuoteService;

import javax.enterprise.inject.Intercepted;
import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.SecurityContext;

import org.eclipse.microprofile.jwt.JsonWebToken;

@Path("/v2/quote")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class QuoteResource {
    /*
     * QUOTE V2 --> IMPORT V2.SERVICE-QUOTE
     */
    @Inject
    QuoteService quoteService;

    @Inject
    JsonWebToken jwt;

    @POST
    public JsonNode quote(@Context SecurityContext securityContext, JsonNode req) throws CustomerException,
            IamException,
            ProductException,
            OrderException, QuotatorException, InternalQuoteException {
            return quoteService.quote(req.get("data"), "Bearer " + jwt.getRawToken());
    }


}
