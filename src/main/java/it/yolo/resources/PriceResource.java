package it.yolo.resources;

import javax.inject.Inject;
import javax.ws.rs.*;
import it.yolo.model.BaseData;
import it.yolo.model.PriceRequest;

import io.quarkus.security.Authenticated;
import it.yolo.model.PriceResponse;
import it.yolo.service.PriceService;

@Path("v1/price")
public class PriceResource {

    @Inject
    PriceService priceService;

    @PUT
    @Path("/coupon/{couponCode}")
    @Authenticated
    public BaseData<PriceResponse> coupon(
            @HeaderParam("Authorization") String token,
            @PathParam("couponCode") String couponCode,
            BaseData<PriceRequest> req
    ) {
        PriceResponse res;
        res = priceService.priceByCoupon(token, couponCode,req.getData());
        return new BaseData<>(res);
    }
}
