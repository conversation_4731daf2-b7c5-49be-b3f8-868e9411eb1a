package it.yolo.resources;

import com.fasterxml.jackson.databind.JsonNode;
import io.quarkus.security.Authenticated;
import it.yolo.entity.WarrantyPremium;
import it.yolo.exception.IamException;
import it.yolo.exception.OrderNotFoundException;
import it.yolo.exception.PriceNotFoundException;
import it.yolo.model.*;
import it.yolo.service.IamService;
import it.yolo.service.OrderService;
import it.yolo.service.WarrantyPricingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.math.BigDecimal;
import java.util.List;

@Path("v1/pricing")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class WarrantyPricingResource {

    private static final Logger logger = LoggerFactory.getLogger(WarrantyPricingResource.class);

    @Inject
    WarrantyPricingService warrantyPricingService;

    @Inject
    OrderService orderService;

    @Inject
    IamService technicalTokenService;

    @POST
    @Path("/order-quote")
    @Authenticated
    public Response getQuoteFromOrder(@Valid BaseData<OrderQuoteRequest> request) {
        logger.info("Received order-quote request: {}", request);
        logger.info("Retrive technical token");

        try {
            String technicalToken = technicalTokenService.getToken();

            OrderQuoteRequest quoteRequest = request.getData();
            String orderCode = quoteRequest.getOrderCode();

            // 1. Recuperare l'ordine completo
            JsonNode orderJson = orderService.getOrder(orderCode, technicalToken);

            // 2. Estrarre le garanzie dall'ordine
            List<WarrantyDetail> warrantyDetails = orderService.extractWarrantyDetailsFromOrder(orderJson);

            if (warrantyDetails.isEmpty()) {
                logger.error("No warranty details found in order: {}", orderCode);
                return Response.status(Response.Status.BAD_REQUEST)
                    .entity(createErrorResponse("No warranty details found in the order"))
                    .build();
            }

            // 3. Estrarre la durata in anni dall'ordine
            int durationYears = orderService.extractDurationYearsFromOrder(orderJson);

            // 4a. Calcolare la quotazione annuale per le garanzie
            List<WarrantyPremium> annualPremiums = warrantyPricingService.getPremiums(
                warrantyDetails,
                durationYears
            );

            // 4b. Calcolare la quotazione mensile per le garanzie
            List<WarrantyPremium> monthlyPremiums = warrantyPricingService.getMonthlyPremiums(
                warrantyDetails
            );

            // 5. Calcolare i totali
            BigDecimal totalAnnualPremium = warrantyPricingService.calculateTotalPremium(annualPremiums);
            BigDecimal totalMonthlyPremium = warrantyPricingService.calculateTotalPremium(monthlyPremiums);
            String premiumCurrency = warrantyPricingService.getPremiumCurrency(annualPremiums);
            String benefitCurrency = warrantyPricingService.getBenefitCurrency(annualPremiums);

            // 6. Aggiornare l'ordine con entrambe le quotazioni calcolate
            return orderService.updateOrderWithQuotation(
                orderCode,
                annualPremiums,
                totalAnnualPremium,
                premiumCurrency,
                benefitCurrency,
                monthlyPremiums,
                totalMonthlyPremium,
                    durationYears,
                    technicalToken
            );

        }catch (IamException ex) {
            logger.error("Failed to retrieve technical token: {}", ex.getMessage());
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(createErrorResponse("Failed to retrieve technical token"))
                .build();
        } catch (OrderNotFoundException e) {
            logger.error("Order not found: {}", e.getMessage());
            return Response.status(Response.Status.NOT_FOUND)
                .entity(createErrorResponse(e.getMessage()))
                .build();

        } catch (PriceNotFoundException e) {
            logger.error("Price not found: {}", e.getMessage());
            return Response.status(Response.Status.NOT_FOUND)
                .entity(createErrorResponse(e.getMessage()))
                .build();

        } catch (IllegalArgumentException e) {
            logger.error("Invalid request parameters: {}", e.getMessage());
            return Response.status(Response.Status.BAD_REQUEST)
                .entity(createErrorResponse(e.getMessage()))
                .build();

        } catch (Exception e) {
            logger.error("Unexpected error processing order-quote request", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(createErrorResponse("Internal server error"))
                .build();
        }
    }

    private BaseData<ErrorResponse> createErrorResponse(String message) {
        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setError(message);
        return new BaseData<>(errorResponse);
    }
}
