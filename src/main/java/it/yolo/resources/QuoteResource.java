package it.yolo.resources;

import javax.inject.Inject;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.core.MediaType;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.service.QuoteService;

@Path("/v1/quote")
public class QuoteResource {


    /*
     * QUOTE V1 --> IMPORT SERVICE-QUOTE
     */
    @Inject
    QuoteService quoteService;

    @PUT
    public JsonNode quote(JsonNode req){
        return quoteService.quote(req);

    }

}
