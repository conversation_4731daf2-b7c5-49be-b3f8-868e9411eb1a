package it.yolo.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DataOrderResponse {
    
    @JsonProperty("orderItem")
    private List<OrderItemResponse> orderItems;

    @JsonProperty("product")
    private ProductResponse productResponse;

    public ProductResponse getProductResponse() {
        return this.productResponse;
    }

    public void setProductResponse(ProductResponse productResponse) {
        this.productResponse = productResponse;
    }


    public List<OrderItemResponse> getOrderItems() {
        return this.orderItems;
    }

    public void setOrderItems(List<OrderItemResponse> orderItems) {
        this.orderItems = orderItems;
    }

}
