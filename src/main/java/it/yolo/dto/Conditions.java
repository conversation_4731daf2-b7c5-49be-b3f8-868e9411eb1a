
package it.yolo.dto;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "items",
    "validTo",
    "validFrom"
})
@Generated("jsonschema2pojo")
public class Conditions {

    @JsonProperty("items")
    private Items items;
    @JsonProperty("validTo")
    private String validTo;
    @JsonProperty("validFrom")
    private String validFrom;

    @JsonProperty("customers")
    private List<String> customers;

    @JsonProperty("priceRule")
    private PriceRule priceRule;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new LinkedHashMap<String, Object>();

    @JsonProperty("items")
    public Items getItems() {
        return items;
    }

    @JsonProperty("items")
    public void setItems(Items items) {
        this.items = items;
    }

    @JsonProperty("validTo")
    public String getValidTo() {
        return validTo;
    }

    @JsonProperty("validTo")
    public void setValidTo(String validTo) {
        this.validTo = validTo;
    }

    @JsonProperty("validFrom")
    public String getValidFrom() {
        return validFrom;
    }

    @JsonProperty("validFrom")
    public void setValidFrom(String validFrom) {
        this.validFrom = validFrom;
    }

    @JsonProperty("customers")
    public List<String> getCustomers() {
        return customers;
    }

    @JsonProperty("customers")
    public void setCustomers(List<String> customers) {
        this.customers = customers;
    }

    @JsonProperty("priceRule")
    public PriceRule getPriceRule() {
        return priceRule;
    }

    @JsonProperty("priceRule")
    public void setPriceRule(PriceRule priceRule) {
        this.priceRule = priceRule;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
