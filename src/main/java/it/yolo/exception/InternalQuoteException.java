package it.yolo.exception;

import com.fasterxml.jackson.databind.JsonNode;

public class InternalQuoteException extends Exception {
    private JsonNode kenticoMessage;

    public InternalQuoteException(JsonNode kenticoMessage) {
        this.kenticoMessage = kenticoMessage;
    }

    public JsonNode getKenticoMessage() {
        return kenticoMessage;
    }

    public void setKenticoMessage(JsonNode kenticoMessage) {
        this.kenticoMessage = kenticoMessage;
    }

   


    


}
