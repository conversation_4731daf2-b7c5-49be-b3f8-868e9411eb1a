package it.yolo.exception;

public class QuotatorException extends Exception{

    //private String providerResponse;
    
    public QuotatorException() {
    }

    public QuotatorException(String message) {
        super(message);
    }

    public QuotatorException(String message, Throwable cause) {
        super(message, cause);
    }

    // public QuotatorException(String message, String providerResponse) {
    //     super(message);
    //     this.providerResponse = providerResponse;
    // }

    // public String getProviderResponse() {
    //     return providerResponse;
    // }

    // public void setProviderResponse(String providerResponse) {
    //     this.providerResponse = providerResponse;
    // }
}
