package it.yolo.exception;
import it.yolo.model.ErrorResponse;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.server.ServerExceptionMapper;

import javax.ws.rs.core.Response;



public class ExceptionMappers {

    private static final Logger LOGGER = Logger.getLogger(ExceptionMappers.class);
    private static final String SEPARATOR = "\n";


    @ServerExceptionMapper(ClientException.class)
    public Response clientException(ClientException ex) {
        LOGGER.error(ex.getStackTrace().toString(), ex);

        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setErrorInfo(ex.getMessage());
        errorResponse.setError(formatStackTrace(ex.getStackTrace()));


        return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(errorResponse).build();
    }



    @ServerExceptionMapper(IamException.class)
    public Response mapException(IamException ex) {
        LOGGER.error(ex.getStackTrace().toString(), ex);

        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setErrorInfo(ex.getMessage());
        errorResponse.setError(formatStackTrace(ex.getStackTrace()));

        return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(errorResponse).build();
    }

    @ServerExceptionMapper(OrderException.class)
    public Response mapExceptionOrder(OrderException ex) {
        LOGGER.error(ex.getStackTrace().toString(), ex);

        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setErrorInfo(ex.getMessage());
        errorResponse.setError(formatStackTrace(ex.getStackTrace()));

        return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(errorResponse).build();
    }

    @ServerExceptionMapper(QuotatorException.class)
    public Response mapExceptionQuotator(QuotatorException ex) {
        LOGGER.error(ex.getStackTrace().toString(), ex);

        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setErrorInfo(ex.getMessage());
        errorResponse.setError(formatStackTrace(ex.getStackTrace()));
        //errorResponse.setProviderResponse(ex.getProviderResponse());

        return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(errorResponse).build();

    }

    @ServerExceptionMapper
    public Response internalQuoteException(InternalQuoteException ex) {
        LOGGER.error("InternalQuoteException: " +  ex.getKenticoMessage().toString());

        return Response.status(422)
                .entity(ex.getKenticoMessage()).build();
    }

    public static String formatStackTrace(StackTraceElement[] stackTrace) {
        StringBuilder buffer = new StringBuilder();
        for (StackTraceElement element : stackTrace) {
            buffer.append(element).append(SEPARATOR);
        }
        return buffer.toString();
    }

}
