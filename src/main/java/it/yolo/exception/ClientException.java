package it.yolo.exception;
import javax.ws.rs.core.Response;

public class ClientException extends RuntimeException{

    private Integer statusCode;
    private String message ;


    private Response res;
    public ClientException(Integer statusCode, String message, Response res) {
        super(message);
        this.statusCode=statusCode;
        this.message=message;
        this.res=res;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Response getRes() {
        return res;
    }

    public void setRes(Response res) {
        this.res = res;
    }
}


