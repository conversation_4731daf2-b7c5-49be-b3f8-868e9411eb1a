package it.yolo.exception;

import java.util.List;

/**
 * Exception thrown when warranty premiums are not found for requested warranty IDs
 */
public class PriceNotFoundException extends RuntimeException {

    private final List<Integer> missingWarrantyIds;

    public PriceNotFoundException(String message) {
        super(message);
        this.missingWarrantyIds = List.of();
    }

    public PriceNotFoundException(String message, List<Integer> missingWarrantyIds) {
        super(message);
        this.missingWarrantyIds = missingWarrantyIds;
    }

    public PriceNotFoundException(String message, Throwable cause) {
        super(message, cause);
        this.missingWarrantyIds = List.of();
    }

    public PriceNotFoundException(String message, List<Integer> missingWarrantyIds, Throwable cause) {
        super(message, cause);
        this.missingWarrantyIds = missingWarrantyIds;
    }

    public List<Integer> getMissingWarrantyIds() {
        return missingWarrantyIds;
    }

    @Override
    public String toString() {
        return "PriceNotFoundException{" +
                "message='" + getMessage() + '\'' +
                ", missingWarrantyIds=" + missingWarrantyIds +
                '}';
    }
}
