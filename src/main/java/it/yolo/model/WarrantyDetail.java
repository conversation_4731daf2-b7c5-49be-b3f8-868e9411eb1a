package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;

public class WarrantyDetail {

    @JsonProperty("id")
    @NotNull(message = "Warranty ID cannot be null")
    private Integer id;

    @JsonProperty("name")
    private String name;    @JsonProperty("benefitAmount")
    @NotNull(message = "Benefit amount cannot be null")
    private String benefitAmount;

    // Constructors
    public WarrantyDetail() {
    }    public WarrantyDetail(Integer id, String name, String benefitAmount) {
        this.id = id;
        this.name = name;
        this.benefitAmount = benefitAmount;
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }    public String getBenefitAmount() {
        return benefitAmount;
    }

    public void setBenefitAmount(String benefitAmount) {
        this.benefitAmount = benefitAmount;
    }

    @Override
    public String toString() {
        return "WarrantyDetail{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", benefitAmount=" + benefitAmount +
                '}';
    }
}
