package it.yolo.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class OrderRequest {

    private Long id;
    private String orderCode;
    @JsonProperty("orderItem")
    private OrderItemRequest orderItemRequest;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("orderItem")
    public OrderItemRequest getOrderItemRequest() {
        return orderItemRequest;
    }

    @JsonProperty("orderItem")
    public void setOrderItemRequest(OrderItemRequest orderItemRequests) {
        this.orderItemRequest = orderItemRequests;
    }
}
