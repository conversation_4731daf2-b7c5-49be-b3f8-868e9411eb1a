package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class WarrantyPriceDetail {

    @JsonProperty("warrantyId")
    private Integer warrantyId;

    @JsonProperty("premium")
    private BigDecimal premium;

    // Constructors
    public WarrantyPriceDetail() {
    }

    public WarrantyPriceDetail(Integer warrantyId, BigDecimal premium) {
        this.warrantyId = warrantyId;
        this.premium = premium;
    }

    // Getters and Setters
    public Integer getWarrantyId() {
        return warrantyId;
    }

    public void setWarrantyId(Integer warrantyId) {
        this.warrantyId = warrantyId;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    @Override
    public String toString() {
        return "WarrantyPriceDetail{" +
                "warrantyId=" + warrantyId +
                ", premium=" + premium +
                '}';
    }
}
