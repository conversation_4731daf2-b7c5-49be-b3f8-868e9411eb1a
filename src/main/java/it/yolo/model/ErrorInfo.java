package it.yolo.model;



public class ErrorInfo {

    private String message;
    private int fastQuoteFailedAttempts;
    private ProviderResponse providerResponse;

    

    public String getMessage() {
        return message;
    }



    public void setMessage(String message) {
        this.message = message;
    }



    public int getFastQuoteFailedAttempts() {
        return fastQuoteFailedAttempts;
    }



    public void setFastQuoteFailedAttempts(int fastQuoteFailedAttempts) {
        this.fastQuoteFailedAttempts = fastQuoteFailedAttempts;
    }



    public ProviderResponse getProviderResponse() {
        return providerResponse;
    }



    public void setProviderResponse(ProviderResponse providerResponse) {
        this.providerResponse = providerResponse;
    }



    public ErrorInfo(String message, int fastQuoteFailedAttempts, ProviderResponse providerResponse) {
        this.message = message;
        this.fastQuoteFailedAttempts = fastQuoteFailedAttempts;
        this.providerResponse = providerResponse;
    }
}
