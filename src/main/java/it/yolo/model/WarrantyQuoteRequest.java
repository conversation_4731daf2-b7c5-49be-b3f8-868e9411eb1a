package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

public class WarrantyQuoteRequest {

    @JsonProperty("warrantyIds")
    @NotNull(message = "Warranty IDs cannot be null")
    @NotEmpty(message = "Warranty IDs cannot be empty")
    private List<Integer> warrantyIds;

    @JsonProperty("durationYears")
    @NotNull(message = "Duration years cannot be null")
    @Positive(message = "Duration years must be positive")
    private Integer durationYears;

    @JsonProperty("benefitAmount")
    @NotNull(message = "Benefit amount cannot be null")
    @Positive(message = "Benefit amount must be positive")
    private BigDecimal benefitAmount;

    // Constructors
    public WarrantyQuoteRequest() {
    }

    public WarrantyQuoteRequest(List<Integer> warrantyIds, Integer durationYears, BigDecimal benefitAmount) {
        this.warrantyIds = warrantyIds;
        this.durationYears = durationYears;
        this.benefitAmount = benefitAmount;
    }

    // Getters and Setters
    public List<Integer> getWarrantyIds() {
        return warrantyIds;
    }

    public void setWarrantyIds(List<Integer> warrantyIds) {
        this.warrantyIds = warrantyIds;
    }

    public Integer getDurationYears() {
        return durationYears;
    }

    public void setDurationYears(Integer durationYears) {
        this.durationYears = durationYears;
    }

    public BigDecimal getBenefitAmount() {
        return benefitAmount;
    }

    public void setBenefitAmount(BigDecimal benefitAmount) {
        this.benefitAmount = benefitAmount;
    }

    @Override
    public String toString() {
        return "WarrantyQuoteRequest{" +
                "warrantyIds=" + warrantyIds +
                ", durationYears=" + durationYears +
                ", benefitAmount=" + benefitAmount +
                '}';
    }
}
