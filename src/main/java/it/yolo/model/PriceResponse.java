package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import it.yolo.dto.CouponResponse;
import it.yolo.dto.DataCouponResponse;
import it.yolo.dto.OrderResponse;

public class PriceResponse {

    @JsonProperty("coupon")
    private CouponResponse couponResponse;

    @JsonProperty("order")
    private Object orderResponse;

    @JsonProperty("coupon")
    public CouponResponse getCouponResponse() {
        return couponResponse;
    }

    @JsonProperty("coupon")
    public void setCouponResponse(CouponResponse couponResponse) {
        this.couponResponse = couponResponse;
    }

    @JsonProperty("order")
    public Object getOrderResponse() {
        return orderResponse;
    }
    @JsonProperty("order")
    public void setOrderResponse(Object orderResponse) {
        this.orderResponse = orderResponse;
    }
}
