package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class TotalQuoteResponse {

    @JsonProperty("totalPremium")
    private BigDecimal totalPremium;

    @JsonProperty("premiumCurrency")
    private String premiumCurrency;

    @JsonProperty("benefitCurrency")
    private String benefitCurrency;

    // Constructors
    public TotalQuoteResponse() {
    }

    public TotalQuoteResponse(BigDecimal totalPremium, String premiumCurrency, String benefitCurrency) {
        this.totalPremium = totalPremium;
        this.premiumCurrency = premiumCurrency;
        this.benefitCurrency = benefitCurrency;
    }

    // Getters and Setters
    public BigDecimal getTotalPremium() {
        return totalPremium;
    }

    public void setTotalPremium(BigDecimal totalPremium) {
        this.totalPremium = totalPremium;
    }

    public String getPremiumCurrency() {
        return premiumCurrency;
    }

    public void setPremiumCurrency(String premiumCurrency) {
        this.premiumCurrency = premiumCurrency;
    }

    public String getBenefitCurrency() {
        return benefitCurrency;
    }

    public void setBenefitCurrency(String benefitCurrency) {
        this.benefitCurrency = benefitCurrency;
    }

    @Override
    public String toString() {
        return "TotalQuoteResponse{" +
                "totalPremium=" + totalPremium +
                ", premiumCurrency='" + premiumCurrency + '\'' +
                ", benefitCurrency='" + benefitCurrency + '\'' +
                '}';
    }
}
