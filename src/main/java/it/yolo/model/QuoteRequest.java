package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

public class QuoteRequest {

    @JsonProperty("productId")
    private Long productId;

    @JsonProperty("orderId")
    private Long orderId;

    @JsonProperty("customerId")
    private Long customerId;

    @JsonProperty("productId")
    public Long getProductId() {
        return productId;
    }

    @JsonProperty("productId")
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    @JsonProperty("orderId")
    public Long getOrderId() {
        return orderId;
    }

    @JsonProperty("orderId")
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    @JsonProperty("customerId")
    public Long getCustomerId() {
        return customerId;
    }
    @JsonProperty("customerId")
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }
}
