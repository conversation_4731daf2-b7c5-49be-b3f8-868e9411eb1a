package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class DetailedQuoteResponse {

    @JsonProperty("premiumDetails")
    private List<WarrantyPriceDetail> premiumDetails;

    @JsonProperty("premiumCurrency")
    private String premiumCurrency;

    @JsonProperty("benefitCurrency")
    private String benefitCurrency;

    // Constructors
    public DetailedQuoteResponse() {
    }

    public DetailedQuoteResponse(List<WarrantyPriceDetail> premiumDetails, String premiumCurrency, String benefitCurrency) {
        this.premiumDetails = premiumDetails;
        this.premiumCurrency = premiumCurrency;
        this.benefitCurrency = benefitCurrency;
    }

    // Getters and Setters
    public List<WarrantyPriceDetail> getPremiumDetails() {
        return premiumDetails;
    }

    public void setPremiumDetails(List<WarrantyPriceDetail> premiumDetails) {
        this.premiumDetails = premiumDetails;
    }

    public String getPremiumCurrency() {
        return premiumCurrency;
    }

    public void setPremiumCurrency(String premiumCurrency) {
        this.premiumCurrency = premiumCurrency;
    }

    public String getBenefitCurrency() {
        return benefitCurrency;
    }

    public void setBenefitCurrency(String benefitCurrency) {
        this.benefitCurrency = benefitCurrency;
    }

    @Override
    public String toString() {
        return "DetailedQuoteResponse{" +
                "premiumDetails=" + premiumDetails +
                ", premiumCurrency='" + premiumCurrency + '\'' +
                ", benefitCurrency='" + benefitCurrency + '\'' +
                '}';
    }
}
