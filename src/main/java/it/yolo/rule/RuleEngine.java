package it.yolo.rule;


import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import it.yolo.common.Utility;
import it.yolo.dto.*;

public class RuleEngine {

    public static boolean process(RuleDetails ruleDetails, OrderResponse orderResponse, String customerEmail) {
        boolean processAction = true;
        if (ruleDetails.getConditions() != null) {
                if(ruleDetails.getConditions().getCustomers() != null && !ruleDetails.getConditions().getCustomers().isEmpty()) {
                    processAction = checkEmail(ruleDetails.getConditions().getCustomers(), customerEmail);
                    if (!processAction) {
                        return false;
                    }
                }
            if (ruleDetails.getConditions().getValidTo() != null
                    && ruleDetails.getConditions().getValidFrom() != null
                    && ruleDetails.getConditions().getItems() != null) {
                processAction = checkDate(ruleDetails.getConditions().getValidTo(),
                        ruleDetails.getConditions().getValidFrom());
                processAction = checkItem(ruleDetails.getConditions().getItems(), orderResponse);
                if (ruleDetails.getConditions().getPriceRule() != null && !ruleDetails.getConditions().getPriceRule().getCondition().isEmpty()) {
                    processAction = checkPrice(ruleDetails.getConditions().getPriceRule(), orderResponse);
                }
                if (ruleDetails.getConditions().getItems() != null && ruleDetails.getConditions().getValidTo() == null
                        && ruleDetails.getConditions().getValidFrom() == null) {
                    processAction = checkItem(ruleDetails.getConditions().getItems(), orderResponse);
                }
            }
        }
        if (processAction) {
            processActions(ruleDetails.getActions(), orderResponse);
        }
        return processAction;
    }

    public static boolean checkDate(String validTo, String validFrom) {
        Date date = new Date();
        Date validateTo = Utility.parseDateFromString(validTo);
        Date validateFrom = Utility.parseDateFromString(validFrom);
        return date.getTime() >= validateFrom.getTime() && date.getTime() <= validateTo.getTime();

    }

    public static boolean checkItem(Items items, OrderResponse orderResponse) {
        boolean result = false;

        if (items.getProducts().stream().filter(p -> p.equalsIgnoreCase(orderResponse.getData()
                .getProductResponse().getDataProductResponse().getCode())).collect(Collectors.toList()).size() > 0) {
            return result = true;
        }

        if (items.getPackets().stream().filter(p -> p.equalsIgnoreCase(orderResponse.getData()
                        .getProductResponse().getDataProductResponse().getPacketResponse().getDataPacketResponse().getName()))
                .collect(Collectors.toList()).size() > 0) {
            return result = true;
        }

        return result;

    }

    public static boolean checkEmail (List<String> emailList, String customerEmail) {
        boolean result = false;
        for (String email : emailList) {
            if (email.trim().equalsIgnoreCase(customerEmail.trim())) {
                return result = true;
            }
        }
        return result;
    }

    public static boolean checkPrice (PriceRule priceRule, OrderResponse orderResponse) {
        boolean result = false;
        if (priceRule.getCondition().equalsIgnoreCase("greater")) {
            Double price = Double.valueOf(orderResponse.getData().getOrderItems().get(0).getPrice());
            Double value = Double.valueOf(priceRule.getValue());
            if (price > value) {
                result = true;
            }
        }
        if (priceRule.getCondition().equalsIgnoreCase("greaterOrEqual")) {
            Double price = Double.valueOf(orderResponse.getData().getOrderItems().get(0).getPrice().replace(",", "."));
            Double value = Double.valueOf(priceRule.getValue().replace(",", "."));
            if (price >= value){
                result = true;
            }
        }
        return result;
    }

    public static void processActions(Actions actions, OrderResponse orderResponse) {
        if (actions.getDiscountType().equalsIgnoreCase("percentage")) {
            Double price = Double.valueOf(orderResponse.getData().getOrderItems().get(0).getPrice());
            String priceFinalOrderItem = Utility.calculatePercentage(actions.getDiscountAmount(), price).toString();
            orderResponse.getData().getOrderItems().forEach(priceItem -> {
            
                priceItem.setPrice(priceFinalOrderItem);
            });
        }

        if (actions.getDiscountType().equalsIgnoreCase("fixed")) {
            Double price = Double.valueOf(orderResponse.getData().getOrderItems().get(0).getPrice());
            String priceFinalOrderItem = String.valueOf(price - Double.valueOf(actions.getDiscountAmount()));
            orderResponse.getData().getOrderItems().forEach(priceItem -> {
                // add this check to solve the negative amount on discounted price if the price after adjust is negative just return 0
                if(Double.parseDouble(priceFinalOrderItem) < 0) {
                    priceItem.setPrice("0");
                } else {
                    priceItem.setPrice(priceFinalOrderItem);
                }
            });
        }

        if (actions.getDiscountType().equalsIgnoreCase("freeTrial")) {
            Double price = Double.valueOf(orderResponse.getData().getOrderItems().get(0).getPrice());
            String priceFinalOrderItem = Utility.calculateFreeTrial(price).toString();
            orderResponse.getData().getOrderItems().forEach(priceItem -> {
                priceItem.setPrice(priceFinalOrderItem);
            });
        }

    }

}
