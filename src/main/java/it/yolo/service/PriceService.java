package it.yolo.service;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import it.yolo.dto.CouponResponse;
import it.yolo.dto.OrderItemResponse;
import it.yolo.model.BaseData;
import it.yolo.model.OrderItemRequest;
import it.yolo.model.PriceRequest;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import com.fasterxml.jackson.databind.JsonNode;

import it.yolo.client.CouponClient;
import it.yolo.client.OrderClient;
import it.yolo.dto.OrderResponse;
import it.yolo.dto.ValidateResponse;
import it.yolo.model.PriceResponse;
import it.yolo.rule.RuleEngine;

@RequestScoped
public class PriceService {

    @Inject
    @RestClient
    CouponClient couponClient;

    @Inject
    @RestClient
    OrderClient orderClient;

    @Inject
    CustomerService customerService;

    public PriceResponse priceByCoupon(String token, String couponCode, PriceRequest req) {

        Response resValidateCoupon = couponClient.getValidateCoupon(token,couponCode,true);
        ValidateResponse validateCouponResponse =resValidateCoupon.readEntity(ValidateResponse.class );
        if(!validateCouponResponse.isData()){
            return null;
        } 

        Response res = couponClient.getCouponResponse(token,couponCode,true);
        CouponResponse couponResponse = res.readEntity(CouponResponse.class);
        if (!couponResponse.getData().getPromotion().getActive()) {
              return null;
        }
        // Get orderorderCode
        Long orderItemId=req.getOrderRequest().getOrderItemRequest()
                            .getId();
        Response response = orderClient.orderDetailsByOrderItemId(token,orderItemId);
        OrderResponse orderResponse = response.readEntity(OrderResponse.class);
        String priceFull = orderResponse.getData().getOrderItems().get(0).getPrice();
        String customerEmail = customerService.findByToken(token);
       boolean canProceed= RuleEngine.process(couponResponse.getData().getPromotion().getRule().getRule(), orderResponse, customerEmail);
        if (!canProceed) {
            throw new IllegalArgumentException("Codice sconto non applicabile");
        }

        BaseData <List<OrderItemResponse>> itemsUpdated = new BaseData (orderResponse.getData().getOrderItems());

        itemsUpdated.getData().forEach(item->{
            ObjectMapper mapper= new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            JsonNode coupon=mapper.convertValue(couponResponse, JsonNode.class);
            ((ObjectNode)coupon).put("priceFull", priceFull);
            ((ObjectNode)coupon).put("priceDiscounted", item.getPrice());
            item.setPromotion(coupon);
        });

        Response responseUpadtePrice = orderClient.updatePriceByOrderItemId(itemsUpdated,token);
        JsonNode orderUpdatedPrice = responseUpadtePrice.readEntity(JsonNode.class);

        PriceResponse priceResponse= new PriceResponse();
        priceResponse.setCouponResponse(couponResponse);
        priceResponse.setOrderResponse(orderUpdatedPrice);
        
        

        return priceResponse;
    }

}
