package it.yolo.service;


import be.digitech.iadtoken.grpc.Empty;
import be.digitech.iadtoken.grpc.IamGrpc;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.grpc.GrpcClient;
import it.yolo.client.CustomerClient;
import it.yolo.exception.CustomerException;
import it.yolo.service.v3.JwtUtilsService;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class CustomerService {

    @Inject
    @RestClient
    CustomerClient customerClient;

    @Inject
    JwtUtilsService jwtUtilsService;

    @GrpcClient
    IamGrpc tokenService;


    public JsonNode findById(long id, String token) throws CustomerException {
        if(jwtUtilsService.extractGroupsCognito().contains("anonimo") || jwtUtilsService.extractGroupsCognito().contains("intermediary-anonimo")){
            Empty empty = null;
            token = "Bearer " + tokenService.technicalToken(empty).await().indefinitely().getToken();
        }
        Response customer = customerClient.findByToken(token);
        Long customerId = customer.readEntity(JsonNode.class).get("data").get("id").asLong();
        try{
            Response res = customerClient.findById(token,customerId);
            return res.readEntity(JsonNode.class);
        }catch(Exception exception){
            throw new CustomerException(exception.getMessage());
        }
    }

    public String findByToken (String token){
        try {
            Response response = customerClient.findByToken(token);
            if (response.getStatus() == 200){
                String customer = response.readEntity(String.class);
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode node = objectMapper.readTree(customer);
                String email = node.path("data").path("primary_mail").asText();
                return email;
            } else {
                return null;
            }
        } catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

}
