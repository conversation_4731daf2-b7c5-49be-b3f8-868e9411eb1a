package it.yolo.service;

import it.yolo.client.ProductClient;
import it.yolo.client.response.ProductResponse;
import it.yolo.exception.ProductException;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.ProcessingException;
import javax.ws.rs.core.Response;

@RequestScoped
public class ProductService {

    @Inject
    @RestClient
    ProductClient productClient;

    public ProductResponse findById(long id, String token) throws ProductException {
        try{
            Response res=productClient.findById(token,id);
            ProductResponse productResponse=res.readEntity(ProductResponse.class);
            return productResponse;
        }catch (Exception exception){
            throw new ProductException(exception.getMessage());
        }
    }


}
