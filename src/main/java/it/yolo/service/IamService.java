package it.yolo.service;


import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.IamClient;
import it.yolo.client.request.LoginRequest;
import it.yolo.exception.IamException;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class IamService {

    @Inject
    @RestClient
    IamClient iamClient;

    @ConfigProperty(name = "tch.user.username")
    String tchUsername;

    @ConfigProperty(name = "tch.user.password")
    String tchPassword;


    public String getToken() throws IamException {
        try{
            LoginRequest loginTch = new LoginRequest();
            loginTch.setPassword(tchPassword);
            loginTch.setUsername(tchUsername);
            String token ="Bearer " + iamClient.login(loginTch).readEntity(JsonNode.class)
                    .get("token").asText();
            return token;
        }catch (Exception exception){
            throw new IamException(exception.getMessage());
        }
    }
}
