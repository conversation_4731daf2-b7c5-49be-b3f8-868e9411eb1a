package it.yolo.service;

import it.yolo.entity.WarrantyPremium;
import it.yolo.exception.PriceNotFoundException;
import it.yolo.model.WarrantyDetail;
import it.yolo.repository.WarrantyPremiumRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

@RequestScoped
public class WarrantyPricingService {

    private static final Logger logger = LoggerFactory.getLogger(WarrantyPricingService.class);

    @Inject
    WarrantyPremiumRepository warrantyPremiumRepository;

    /**
     * Calcola i premi per le garanzie specificate con la durata in anni
     *
     * @param warrantyDetails Lista di dettagli delle garanzie, ciascuna con il proprio importo di beneficio
     * @param durationYears Durata in anni (convertita in mesi)
     * @return Lista dei premi calcolati
     * @throws PriceNotFoundException se i premi non vengono trovati per tutte le garanzie
     */
    public List<WarrantyPremium> getPremiums(List<WarrantyDetail> warrantyDetails, int durationYears) {
        logger.info("Getting premiums for warrantyDetails: {}, durationYears: {}", warrantyDetails, durationYears);

        // Converti anni in mesi
        int durationMonths = durationYears * 12;
        logger.debug("Converted duration: {} years = {} months", durationYears, durationMonths);

        // Validazione input
        if (warrantyDetails == null || warrantyDetails.isEmpty()) {
            throw new IllegalArgumentException("Warranty details list cannot be null or empty");
        }
        
        if (durationYears <= 0) {
            throw new IllegalArgumentException("Duration years must be positive");
        }
        
        // Validazione degli importi di beneficio
        for (WarrantyDetail detail : warrantyDetails) {
            if (detail.getBenefitAmount() == null || detail.getBenefitAmount().trim().isEmpty()) {
                throw new IllegalArgumentException("Benefit amount cannot be null or empty for warranty ID: " + detail.getId());
            }
            // Per i valori numerici, verifichiamo che siano positivi
            BigDecimal numericAmount = convertBenefitAmountToBigDecimal(detail.getBenefitAmount());
            if (numericAmount != null && numericAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new IllegalArgumentException("Benefit amount must be positive for warranty ID: " + detail.getId());
            }
        }

        // Verifica se esistono premi per tutte le garanzie
        List<WarrantyDetail> missingWarrantyDetails = warrantyPremiumRepository.findMissingWarrantyDetails(
            warrantyDetails, durationMonths);

        if (!missingWarrantyDetails.isEmpty()) {
            String message = String.format(
                "Premiums not found for warranties: %s with duration: %d months",
                missingWarrantyDetails.stream()
                    .map(d -> String.format("(id: %d, benefitAmount: %s)", d.getId(), d.getBenefitAmount()))
                    .collect(Collectors.joining(", ")),
                durationMonths);
            logger.error(message);
            throw new PriceNotFoundException(message,
                missingWarrantyDetails.stream()
                    .map(WarrantyDetail::getId)
                    .collect(Collectors.toList()));
        }

        // Recupera tutti i premi
        List<WarrantyPremium> premiums = warrantyPremiumRepository.findPremiumsForWarranties(
            warrantyDetails, durationMonths);

        logger.info("Successfully retrieved {} premiums", premiums.size());
        return premiums;
    }

    /**
     * Calcola i premi mensili per le garanzie specificate
     *
     * @param warrantyDetails Lista di dettagli delle garanzie, ciascuna con il proprio importo di beneficio
     * @return Lista dei premi mensili calcolati
     * @throws PriceNotFoundException se i premi non vengono trovati per tutte le garanzie
     */
    public List<WarrantyPremium> getMonthlyPremiums(List<WarrantyDetail> warrantyDetails) {
        logger.info("Getting monthly premiums for warrantyDetails: {}", warrantyDetails);

        // Per i premi mensili, impostiamo la durata a 1 mese
        int durationMonths = 1;

        // Validazione input
        if (warrantyDetails == null || warrantyDetails.isEmpty()) {
            throw new IllegalArgumentException("Warranty details list cannot be null or empty");
        }

        // Validazione degli importi di beneficio
        for (WarrantyDetail detail : warrantyDetails) {
            if (detail.getBenefitAmount() == null || detail.getBenefitAmount().trim().isEmpty()) {
                throw new IllegalArgumentException("Benefit amount cannot be null or empty for warranty ID: " + detail.getId());
            }
            // Per i valori numerici, verifichiamo che siano positivi
            BigDecimal numericAmount = convertBenefitAmountToBigDecimal(detail.getBenefitAmount());
            if (numericAmount != null && numericAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new IllegalArgumentException("Benefit amount must be positive for warranty ID: " + detail.getId());
            }
        }

        // Verifica se esistono premi mensili per tutte le garanzie
        List<WarrantyDetail> missingWarrantyDetails = warrantyPremiumRepository.findMissingWarrantyDetails(
            warrantyDetails, durationMonths);

        if (!missingWarrantyDetails.isEmpty()) {
            logger.warn("Monthly premiums not found for some warranties, will calculate from annual premiums");

            // Se non abbiamo i premi mensili, calcoliamo dai premi annuali dividendo per 12
            List<WarrantyPremium> annualPremiums = getPremiums(warrantyDetails, 1);

            // Convertiamo i premi annuali in premi mensili
            return annualPremiums.stream().map(annual -> {
                WarrantyPremium monthly = new WarrantyPremium();
                monthly.setWarrantyId(annual.getWarrantyId());
                monthly.setBenefitAmount(annual.getBenefitAmount());
                monthly.setDurationMonths(1); // Impostiamo la durata a 1 mese

                // Calcoliamo il premio mensile dividendo il premio annuale per 12
                BigDecimal monthlyPremium = annual.getTotalPremium()
                    .divide(new BigDecimal("12"), 2, RoundingMode.HALF_UP);
                monthly.setTotalPremium(monthlyPremium);

                monthly.setBenefitCurrency(annual.getBenefitCurrency());
                monthly.setPremiumCurrency(annual.getPremiumCurrency());

                return monthly;
            }).collect(Collectors.toList());
        }

        // Recupera tutti i premi mensili
        List<WarrantyPremium> premiums = warrantyPremiumRepository.findPremiumsForWarranties(
            warrantyDetails, durationMonths);

        logger.info("Successfully retrieved {} monthly premiums", premiums.size());
        return premiums;
    }

    /**
     * Calcola il premio totale da una lista di premi
     *
     * @param premiums Lista di premi
     * @return Importo totale del premio
     */
    public BigDecimal calculateTotalPremium(List<WarrantyPremium> premiums) {
        if (premiums == null || premiums.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal total = premiums.stream()
            .map(WarrantyPremium::getTotalPremium)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        logger.debug("Calculated total premium: {} from {} premiums", total, premiums.size());
        return total;
    }

    /**
     * Ottiene la valuta del premio dal primo premio (assumendo che tutti abbiano la stessa valuta)
     *
     * @param premiums Lista di premi
     * @return Codice valuta del premio o "EUR" come default
     */
    public String getPremiumCurrency(List<WarrantyPremium> premiums) {
        if (premiums == null || premiums.isEmpty()) {
            return "EUR";
        }
        
        return premiums.get(0).getPremiumCurrency();
    }

    /**
     * Ottiene la valuta del beneficio dal primo premio (assumendo che tutti abbiano la stessa valuta)
     *
     * @param premiums Lista di premi
     * @return Codice valuta del beneficio o "EUR" come default
     */
    public String getBenefitCurrency(List<WarrantyPremium> premiums) {
        if (premiums == null || premiums.isEmpty()) {
            return "EUR";
        }

        return premiums.get(0).getBenefitCurrency();
    }

    /**
     * Converte un importo di beneficio da String a BigDecimal se possibile
     * 
     * @param benefitAmount L'importo di beneficio come stringa
     * @return BigDecimal se convertibile, null altrimenti
     */
    private BigDecimal convertBenefitAmountToBigDecimal(String benefitAmount) {
        if (benefitAmount == null || benefitAmount.trim().isEmpty()) {
            return null;
        }
        
        try {
            return new BigDecimal(benefitAmount.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
