package it.yolo.service;

import it.yolo.entity.WarrantyPremium;
import it.yolo.exception.PriceNotFoundException;
import it.yolo.repository.WarrantyPremiumRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.List;

@RequestScoped
public class WarrantyPricingService {

    private static final Logger logger = LoggerFactory.getLogger(WarrantyPricingService.class);

    @Inject
    WarrantyPremiumRepository warrantyPremiumRepository;

    /**
     * Get warranty premiums for the given parameters
     * 
     * @param warrantyIds List of warranty IDs
     * @param durationYears Duration in years (will be converted to months)
     * @param benefitAmount Benefit amount
     * @return List of warranty premiums
     * @throws PriceNotFoundException if premiums are not found for all warranty IDs
     */
    public List<WarrantyPremium> getPremiums(List<Integer> warrantyIds, int durationYears, BigDecimal benefitAmount) {
        logger.info("Getting premiums for warrantyIds: {}, durationYears: {}, benefitAmount: {}", 
                   warrantyIds, durationYears, benefitAmount);

        // Convert years to months
        int durationMonths = durationYears * 12;
        logger.debug("Converted duration: {} years = {} months", durationYears, durationMonths);

        // Validate input
        if (warrantyIds == null || warrantyIds.isEmpty()) {
            throw new IllegalArgumentException("Warranty IDs list cannot be null or empty");
        }
        
        if (durationYears <= 0) {
            throw new IllegalArgumentException("Duration years must be positive");
        }
        
        if (benefitAmount == null || benefitAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Benefit amount must be positive");
        }

        // Check if premiums exist for all warranty IDs
        List<Integer> missingWarrantyIds = warrantyPremiumRepository.findMissingWarrantyIds(
            warrantyIds, benefitAmount, durationMonths);

        if (!missingWarrantyIds.isEmpty()) {
            String message = String.format(
                "Premiums not found for warranty IDs: %s with benefit amount: %s and duration: %d months", 
                missingWarrantyIds, benefitAmount, durationMonths);
            logger.error(message);
            throw new PriceNotFoundException(message, missingWarrantyIds);
        }

        // Retrieve all premiums
        List<WarrantyPremium> premiums = warrantyPremiumRepository.findByWarrantyIdsAndBenefitAmountAndDurationMonths(
            warrantyIds, benefitAmount, durationMonths);

        logger.info("Successfully retrieved {} premiums", premiums.size());
        return premiums;
    }

    /**
     * Calculate total premium amount from a list of warranty premiums
     * 
     * @param premiums List of warranty premiums
     * @return Total premium amount
     */
    public BigDecimal calculateTotalPremium(List<WarrantyPremium> premiums) {
        if (premiums == null || premiums.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal total = premiums.stream()
            .map(WarrantyPremium::getTotalPremium)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        logger.debug("Calculated total premium: {} from {} premiums", total, premiums.size());
        return total;
    }

    /**
     * Get currency from the first premium (assuming all premiums have the same currency)
     * 
     * @param premiums List of warranty premiums
     * @return Currency code or "EUR" as default
     */
    public String getCurrency(List<WarrantyPremium> premiums) {
        if (premiums == null || premiums.isEmpty()) {
            return "EUR";
        }
        
        return premiums.get(0).getCurrency();
    }
}
