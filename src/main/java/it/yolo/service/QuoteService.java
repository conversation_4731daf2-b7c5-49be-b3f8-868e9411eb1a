package it.yolo.service;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.JsonNode;

import it.yolo.client.AdapterClient;
import it.yolo.client.QuoteClient;

@RequestScoped
public class QuoteService {

    private static final Logger log = LoggerFactory.getLogger(QuoteService.class);

    @Inject
    @RestClient
    QuoteClient quoteClient;

    @Inject
    @RestClient
    AdapterClient adapterClient;

    public JsonNode quote(JsonNode req) {
        log.info("Procedo a quotare");
        JsonNode reqToQuote = adapterClient.toPg(req);
        log.info("Requst to pg : {}", reqToQuote);
        JsonNode respFromPg = quoteClient.quote(reqToQuote);
        log.info("Resp fropm pg : {}", reqToQuote);
        return adapterClient.fromPg(respFromPg);
    }

}
