package it.yolo.service.v3;


import be.digitech.iadtoken.grpc.Empty;
import be.digitech.iadtoken.grpc.IamGrpc;
import com.fasterxml.jackson.databind.JsonNode;
import io.quarkus.grpc.GrpcClient;
import it.yolo.client.OrderClientV3;
import it.yolo.client.response.OrderResponseDto;
import it.yolo.exception.OrderException;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class OrderService {

    @Inject
    @RestClient
    OrderClientV3 orderClient;

    @Inject
    JwtUtilsService jwtUtilsService;

    @GrpcClient
    IamGrpc tokenService;

    public OrderResponseDto findById(long orderId,String token) throws OrderException {
        try{
            OrderResponseDto res= orderClient.findById(orderId,token).readEntity(OrderResponseDto.class);
            return res;
        }catch(Exception exception){
            throw new OrderException(exception.getMessage());
        }
    }

    public JsonNode getById(long orderId,String token) throws OrderException {
        try{
            if(jwtUtilsService.extractGroupsCognito().contains("anonimo") || jwtUtilsService.extractGroupsCognito().contains("intermediary-anonimo")){
                Empty empty = null;
                token = "Bearer " + tokenService.technicalToken(empty).await().indefinitely().getToken();
            }
            JsonNode res = orderClient.findById(orderId,token).readEntity(JsonNode.class);
            return res;
        }catch(Exception exception){
            throw new OrderException(exception.getMessage());
        }
    }

    public JsonNode getByCode(String orderCode, String token) throws OrderException {
        try {
            JsonNode res = orderClient.findByCode(orderCode, token).readEntity(JsonNode.class);
            return res;
        } catch(Exception exception){
            throw new OrderException(exception.getMessage());
        }
    }

    public JsonNode update(JsonNode orderRequest,String token, String orderCode) throws OrderException {
        try{
            JsonNode res= orderClient.updateOrder(token, orderRequest, orderCode).readEntity(JsonNode.class);
            return res;
        }catch(Exception exception){
            throw new OrderException(exception.getMessage());
        }
    }

    public JsonNode updateQuotation(String orderCode, JsonNode quoteResponse, String token) throws OrderException {
        try{
            return orderClient.updateQuotation(orderCode,quoteResponse,token).readEntity(JsonNode.class);
        }catch(Exception exception){
            throw new OrderException(exception.getMessage());
        }
    }

}
