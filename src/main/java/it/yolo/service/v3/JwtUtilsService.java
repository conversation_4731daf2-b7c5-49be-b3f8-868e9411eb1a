package it.yolo.service.v3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.json.JsonString;
import java.io.IOException;
import java.util.*;

@RequestScoped
public class JwtUtilsService {
    @Inject
    JsonWebToken jsonWebToken;

    public Set<String> extractGroupsCognito() {
        List<JsonString> groupsJson=jsonWebToken.getClaim("cognito:groups");
        Set<String> groups=new HashSet<>();
        for (JsonString group : groupsJson) {
            groups.add(group.getString());
        }
        return groups;
    }

    public String extractUsernameFromAnotherToken(String token){
        String payload = token.split("\\.")[1];
        ObjectMapper mapper = new ObjectMapper();
        JsonNode customer = JsonNodeFactory.instance.objectNode();
        try {
            customer = mapper.readTree(Base64.getDecoder().decode(payload));
        } catch (IOException e) {
            return "";
        }
        return customer.get("username").asText();
    }
}
