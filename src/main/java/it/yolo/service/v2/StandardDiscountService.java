package it.yolo.service.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.OrderClient;
import it.yolo.dto.OrderItemResponse;
import it.yolo.exception.OrderException;
import it.yolo.model.BaseData;
import it.yolo.model.DiscountRequest;
import it.yolo.model.OrderRequest;
import it.yolo.model.PriceRequest;
import it.yolo.service.PriceService;
import it.yolo.service.v3.OrderService;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

/**
 * Service for standard discount and coupon management
 */
@RequestScoped
public class StandardDiscountService {

    private static final Logger LOG = Logger.getLogger(StandardDiscountService.class);
    private static final String DATA_PATH = "data";
    private static final String ORDER_ITEM_PATH = "orderItem";
    private static final String PROMOTION_PATH = "promotion";
    private static final String INSTANCE_PATH = "instance";
    private static final String QUOTATION_PATH = "quotation";
    private static final String DISCOUNTED_TOTAL_PATH = "discountedTotal";

    @Inject
    PriceService priceService;

    @Inject
    OrderService orderService;

    @Inject
    @RestClient
    OrderClient orderClient;

    /**
     * Apply a standard discount to an order using a coupon code
     *
     * @param token Authorization token
     * @param request Request containing orderCode and discountCode
     * @return Updated order with applied discount
     */
    public JsonNode standardDiscount(String token, DiscountRequest request) throws OrderException, JsonProcessingException {
        LOG.infof("Applying standard discount with coupon code %s to order %s", request.getDiscountCode(), request.getOrderCode());

        // Retrieve the order and verify it's not already discounted
        JsonNode orderByCodeResponse = orderService.getByCode(request.getOrderCode(), token);
        validatePromotionNotAlreadyUsed(orderByCodeResponse);

        // Normalize the order response and calculate the discounted price
        PriceRequest priceRequest = normalizeGetOrderResponse(orderByCodeResponse);
        JsonNode priceOrderResponse = (JsonNode) priceService.priceByCoupon(token, request.getDiscountCode(), priceRequest).getOrderResponse();

        // Update the order quotation with the discounted price
        updateOrderQuotationWithDiscountedPrice(token, request, priceOrderResponse);

        // Return the updated order
        return orderService.getByCode(request.getOrderCode(), token);
    }

    /**
     * Remove a coupon from an order
     *
     * @param token Authorization token
     * @param orderCode Order code
     * @return Updated order without coupon
     */
    public JsonNode deleteCoupon(String token, String orderCode) throws OrderException {
        LOG.infof("Deleting coupon from order %s", orderCode);

        // Retrieve the order by code
        JsonNode orderByCodeResponse = orderService.getByCode(orderCode, token);
        JsonNode orderData = orderByCodeResponse.get(DATA_PATH);
        JsonNode orderItems = orderData.get(ORDER_ITEM_PATH);
        JsonNode firstOrderItem = orderItems.get(0);

        // Verify that a promotion node exists in the orderItem
        if (firstOrderItem.get(PROMOTION_PATH).isNull()) {
            throw new OrderException("Order is not discounted");
        }

        // Update the discountedTotal field in the quotation node setting it to null
        updateQuotationDiscountedTotal(token, orderCode, firstOrderItem);

        try {
            // Create an OrderItemResponse directly with the orderItem ID
            OrderItemResponse orderItem = new OrderItemResponse();
            orderItem.setId(firstOrderItem.get("id").asLong());
            orderItem.setPromotion(null);

            // Update the order through the client
            orderClient.updatePriceByOrderItemId(new BaseData<>(List.of(orderItem)), token);

            // Return the updated order
            return orderService.getByCode(orderCode, token);
        } catch (Exception e) {
            LOG.errorf("Error updating order: %s", e.getMessage());
            throw new OrderException("Error updating order: " + e.getMessage());
        }
    }

    /**
     * Update the discountedTotal field in the quotation setting it to null
     */
    private void updateQuotationDiscountedTotal(String token, String orderCode, JsonNode orderItemNode) throws OrderException {
        JsonNode quotation = orderItemNode.path(INSTANCE_PATH).path(QUOTATION_PATH);
        if (!quotation.isMissingNode() && !quotation.isNull()) {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode quotationCopy = quotation.deepCopy();

            if (quotationCopy.has(DATA_PATH) && quotationCopy.get(DATA_PATH).isArray() && quotationCopy.get(DATA_PATH).size() > 0) {
                ((ObjectNode) quotationCopy.get(DATA_PATH).get(0)).putNull(DISCOUNTED_TOTAL_PATH);
                orderService.updateQuotation(orderCode, quotationCopy, token);
            }
        }
    }

    /**
     * Verify that the order doesn't already have a discount applied
     */
    private static void validatePromotionNotAlreadyUsed(JsonNode orderByCodeResponse) throws OrderException {
        if (!orderByCodeResponse.get(DATA_PATH).get(ORDER_ITEM_PATH).get(0).get(PROMOTION_PATH).isNull()) {
            throw new OrderException("Order is already discounted");
        }
    }

    /**
     * Update the order quotation with the discounted price
     */
    private void updateOrderQuotationWithDiscountedPrice(String token, DiscountRequest request, JsonNode orderResponse) throws OrderException {
        orderResponse = orderResponse.get(DATA_PATH).get(0);
        JsonNode orderItem = orderResponse.get(ORDER_ITEM_PATH).get(0);
        JsonNode oldQuote = orderItem.get(INSTANCE_PATH).get(QUOTATION_PATH);

        if (!oldQuote.isNull()) {
            JsonNode priceDiscounted = orderItem.get(PROMOTION_PATH).get("priceDiscounted");
            ((ObjectNode) oldQuote.get(DATA_PATH).get(0)).replace(DISCOUNTED_TOTAL_PATH, priceDiscounted);
            orderService.updateQuotation(request.getOrderCode(), oldQuote, token);
        }
    }

    /**
     * Normalize the order response for the price request
     */
    private static PriceRequest normalizeGetOrderResponse(JsonNode orderRequest) throws JsonProcessingException {
        orderRequest = orderRequest.get(DATA_PATH);
        ((ObjectNode) orderRequest).set(ORDER_ITEM_PATH, orderRequest.get(ORDER_ITEM_PATH).get(0));
        ((ObjectNode) orderRequest).retain(Arrays.asList("id", "orderCode", ORDER_ITEM_PATH));
        ((ObjectNode) orderRequest.get(ORDER_ITEM_PATH)).retain(Arrays.asList("id"));

        PriceRequest priceRequest = new PriceRequest();
        priceRequest.setOrderRequest(new ObjectMapper().treeToValue(orderRequest, OrderRequest.class));
        return priceRequest;
    }
}
