package it.yolo.service.v2;

import be.digitech.iadtoken.grpc.Empty;
import be.digitech.iadtoken.grpc.IamGrpc;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.quarkus.grpc.GrpcClient;
import it.yolo.client.response.ProductResponse;
import it.yolo.exception.*;
import it.yolo.quote.*;
import it.yolo.service.CustomerService;
import it.yolo.service.IamService;
import it.yolo.service.ProductService;
import it.yolo.service.v3.JwtUtilsService;
import it.yolo.service.v3.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.PostConstruct;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import java.util.HashMap;
import java.util.Map;

@RequestScoped
public class QuoteService {

    private static final Logger logger = LoggerFactory.getLogger(QuoteService.class);


    @Inject
    IamService iamService;

    @Inject
    ProductService productService;

    @Inject
    OrderService orderService;

    @Inject
    CustomerService customerService;

    @Inject
    Internal internal;
    @Inject
    External external;
    @Inject
    ConfigurationPath configurationPath;

    @Inject
    PacketDurationPrice packetDurationPrice;

    @Inject
    JwtUtilsService jwtUtilsService;

    @GrpcClient
    IamGrpc tokenService;

    Map<String, Quotator> quote;

    @PostConstruct
    void init() {
        quote = new HashMap<>();
        quote.put("internal", internal);
        quote.put("external", external);
        quote.put("configurationPath", configurationPath);
        quote.put("packetDurationPrice", packetDurationPrice);
    }

    public JsonNode quote(JsonNode req, String token) throws IamException, ProductException, OrderException, CustomerException, QuotatorException, InternalQuoteException {
        String ordeCode;
        //String token;
        String quotationType;
        long productId = req.get("productId").asLong();
        long customerId=req.get("customerId").asLong();
        long orderId=req.get("orderId").asLong();
        //token=iamService.getToken();

        logger.info("v2.QuoteService.quote start "+ req);

        logger.info("v2.QuoteService.quote call iad-product start"+ req);
        ProductResponse productResponse= productService.findById(productId,token);
        logger.info("v2.QuoteService.quote call iad-product end "+ productResponse);
        String productCode=productResponse.getData().getCode();

        logger.info("v2.QuoteService.quote call iad-order start"+ req);
        JsonNode orderResponseDto=orderService.getById(orderId,token);
        ordeCode=orderResponseDto.get("data").get("orderCode").asText();
        if(req.has("order")){
            orderResponseDto=orderService.update(req.get("order"), token, ordeCode);
        }
        if (!orderResponseDto.get("data").has("product")){
            JsonNode productNode=new ObjectMapper().getNodeFactory().pojoNode(productResponse);
            ((ObjectNode)orderResponseDto.get("data")).set("product", productNode);
        }
        logger.info("v2.QuoteService.quote call iad-order end "+ orderResponseDto);

        logger.info("v2.QuoteService.quote call iad-customer start"+ req);
        JsonNode customerResponse= customerService.findById(customerId,token);
        logger.info("v2.QuoteService.quote call iad-customer end "+ customerResponse);

        QuotatorDto quotatorDto= new QuotatorDto();
        quotatorDto.setCustomer(customerResponse);
        quotatorDto.setOrderResponse(orderResponseDto);
        quotatorDto.setProductResponse(productResponse);

        quotationType= productResponse.getData()
                        .getConfiguration()
                .getQuotation();


        logger.info("v2.QuoteService.quote call quotator start"+ quotatorDto);
        if(jwtUtilsService.extractGroupsCognito().contains("anonimo") || jwtUtilsService.extractGroupsCognito().contains("intermediary-anonimo")){
            Empty empty = null;
            token = "Bearer " + tokenService.technicalToken(empty).await().indefinitely().getToken();
        }
        JsonNode resQuote=quote.get(quotationType).quote(quotatorDto, token, ordeCode, productCode);
        JsonNode reqUpdateOrderItem= JsonNodeFactory.instance.objectNode();
        ((ObjectNode)reqUpdateOrderItem).set("data", resQuote);
        logger.info("v2.QuoteService.quote call quotator end"+ resQuote);
        logger.info("v2.QuoteService.quote call iad-order/quotation"+ ordeCode);
        JsonNode quotatedOrder=orderService.updateQuotation(ordeCode, reqUpdateOrderItem,token);
        logger.info("v2.QuoteService.quote call iad-order/quotation end "+ ordeCode);
        ((ObjectNode)reqUpdateOrderItem).set("dataOrder", quotatedOrder);
        return reqUpdateOrderItem;
    }
}
