package it.yolo.quote;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;

import it.yolo.client.EnginePriceClient;
import it.yolo.client.request.PgQuotationData;
import it.yolo.client.request.PgQuotationRequest;
import it.yolo.exception.InternalQuoteException;
import it.yolo.exception.OrderException;
import it.yolo.exception.QuotatorException;
import it.yolo.model.ErrorInfo;
import it.yolo.model.ProviderResponse;
import it.yolo.service.v3.OrderService;


import org.eclipse.microprofile.config.ConfigProvider;
import org.eclipse.microprofile.rest.client.RestClientBuilder;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;
import java.net.URL;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.Response;

@RequestScoped
public class Internal implements Quotator{

    @Inject
    Logger logger;

    @Inject
    OrderService orderService;

    @Override
    public JsonNode quote(QuotatorDto quotatorDto, String token, String ordeCode, String productCode) throws InternalQuoteException, QuotatorException {


        logger.info("Internal.quote start + " + quotatorDto);
        PgQuotationData data=new PgQuotationData(quotatorDto.getCustomer(),
                quotatorDto.getOrderResponse(),
                quotatorDto.getProductResponse());
        PgQuotationRequest requestPg=new PgQuotationRequest();
        requestPg.setData(data);

        try{
            logger.info("Internal.quote call internalQuote + " + requestPg);

            String configKey = "quarkus.rest-client." + productCode + ".engine-price.url";

            String productUrl = ConfigProvider.getConfig().getValue(configKey, String.class);

            URL baseUrl = new URL(productUrl);

            EnginePriceClient enginePriceClient = RestClientBuilder.newBuilder().baseUrl(baseUrl).build(EnginePriceClient.class);
            System.out.println("Request directed to custom baseURL  ----> " + baseUrl);
            logger.info("Request directed to custom baseURL  ----> " + baseUrl);
            
            Response res= enginePriceClient.inQuote(requestPg, token);
            JsonNode quoteResponse= res.readEntity(JsonNode.class);
            logger.info("Internal.quote internalQuote end  + " + quoteResponse);
            return  quoteResponse;
        } catch (WebApplicationException exception) {
            logger.error("Errore durante la chiamata a v2.QuoteService.quote per l'ordine: " + ordeCode, exception);
            
            JsonNode errorMessage = exception.getResponse().readEntity(JsonNode.class);
            
            // ObjectMapper objectMapper = new ObjectMapper();
            // try {
            //     JsonNode errorMessageNode = objectMapper.readTree(errorMessage);
                
            //     String message = errorMessageNode.get("message").asText();
            //     int fastQuoteFailedAttempts = errorMessageNode.get("fastQuoteFailedAttempts").asInt();
            //     String providerResponseString = errorMessageNode.get("providerResponse").asText();
            //     JsonNode providerResponseNode = objectMapper.readTree(providerResponseString);
            //     String code = providerResponseNode.get("code").asText();
            //     String description = providerResponseNode.get("description").asText();
                
            //     ErrorInfo errorInfo = new ErrorInfo(message, fastQuoteFailedAttempts, new ProviderResponse(code, description));
                
            //     // Serialize errorInfo into JSON format
            //     String jsonErrorInfo = objectMapper.writeValueAsString(errorInfo);
                
            //     // Update instance.quotation.data with errorInfo
            //     JsonNode reqUpdateOrderItem = JsonNodeFactory.instance.objectNode();
            //     ((ObjectNode) reqUpdateOrderItem).set("data", objectMapper.readTree(jsonErrorInfo));
            //     orderService.updateQuotation(ordeCode, reqUpdateOrderItem, token);               
            // } catch (JsonProcessingException e) {
            //     logger.error("Errore durante il parsing del JSON dell'errore", e);
            //     throw new QuotatorException("Errore durante il parsing del JSON dell'errore", e);
            // } catch (OrderException e) {
            //     throw new OrderException(exception.getMessage());
            // } catch (Exception e) {
            //     logger.error("Errore durante la gestione della quotazione per l'ordine: " + ordeCode, e);
            //     throw new QuotatorException(e.getMessage());
            // 
            throw new InternalQuoteException(errorMessage); 
        }
        catch(Exception exception){
            throw new QuotatorException(exception.getMessage());
        }
    }
}
