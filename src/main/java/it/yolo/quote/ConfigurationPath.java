package it.yolo.quote;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.NullNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.exception.InternalQuoteException;
import it.yolo.exception.QuotatorException;
import it.yolo.service.v3.OrderService;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class ConfigurationPath implements Quotator{
    @Inject
    Logger logger;

    @Inject
    OrderService orderService;

    @Override
    public JsonNode quote(QuotatorDto quotatorDto, String token, String ordeCode, String productCode) throws QuotatorException, InternalQuoteException {
        long price = quotatorDto.getOrderResponse().at(quotatorDto.getProductResponse().getData().getConfiguration().getProperties().get("quotation").get("path").asText()).asLong();
        ArrayNode response = JsonNodeFactory.instance.arrayNode(1);
        ObjectNode total = JsonNodeFactory.instance.objectNode();
        total.put("total", price);
        total.put("discountedTotal", NullNode.getInstance());
        total.put("currency", "€");
        response.add(total);
        return response;
    }
}

/*
{
	"data": [
		{
			"currency": "€",
			"discountedTotal": null,
			"nationalHealthCareTotal": null,
			"netTotal": "13.60",
			"providerResponse": {
				"dettaglioBeni": [
					{
						"dettaglioGaranzie": [
							{
								"codiceGaranzia": "001/00",
								"imp_Lordo_Garanzia": 5.6,
								"imp_Netto_Garanzia": 4.58,
								"imp_Tasse_Garanzia": 1.02
							},
							{
								"codiceGaranzia": "023/00",
								"imp_Lordo_Garanzia": 1.8,
								"imp_Netto_Garanzia": 1.47,
								"imp_Tasse_Garanzia": 0.33
							},
							{
								"codiceGaranzia": "063/00",
								"imp_Lordo_Garanzia": 7,
								"imp_Netto_Garanzia": 5.73,
								"imp_Tasse_Garanzia": 1.27
							},
							{
								"codiceGaranzia": "011/00",
								"imp_Lordo_Garanzia": 2,
								"imp_Netto_Garanzia": 1.82,
								"imp_Tasse_Garanzia": 0.18
							}
						],
						"imp_Lordo_Bene": 16.4,
						"imp_Netto_Bene": 13.6,
						"imp_Tasse_Bene": 2.8,
						"progressivoBene": "1"
					}
				],
				"errori": null,
				"esito": "OK",
				"imp_Lordo_Annuo": 16.4,
				"imp_Netto_Annuo": 13.6,
				"imp_Tasse_Annuo": 2.8,
				"numeroPolizza": "0",
				"numeroProposta": null,
				"segnalazioni": null
			},
			"taxesTotal": "2.80",
			"total": "16.40",
			"warranties": [
				{
					"code": "DA",
					"discountedTotal": null,
					"maximal": null,
					"nationalHealthCareTotal": null,
					"netTotal": "4.58",
					"selected": true,
					"taxesTotal": "1.02",
					"total": "5.60"
				},
				{
					"code": "DC",
					"discountedTotal": null,
					"maximal": null,
					"nationalHealthCareTotal": null,
					"netTotal": "1.47",
					"selected": true,
					"taxesTotal": "0.33",
					"total": "1.80"
				},
				{
					"code": "RCVP",
					"discountedTotal": null,
					"maximal": null,
					"nationalHealthCareTotal": null,
					"netTotal": "5.73",
					"selected": true,
					"taxesTotal": "1.27",
					"total": "7.00"
				},
				{
					"code": "ASS",
					"discountedTotal": null,
					"maximal": null,
					"nationalHealthCareTotal": null,
					"netTotal": "1.82",
					"selected": true,
					"taxesTotal": "0.18",
					"total": "2.00"
				},
				{
					"code": "FER",
					"discountedTotal": null,
					"maximal": null,
					"nationalHealthCareTotal": null,
					"netTotal": "5.73",
					"selected": false,
					"taxesTotal": "1.27",
					"total": "7.00"
				},
				{
					"code": "PP",
					"discountedTotal": null,
					"maximal": null,
					"nationalHealthCareTotal": null,
					"netTotal": "2.47",
					"selected": false,
					"taxesTotal": "0.53",
					"total": "3.00"
				},
				{
					"code": "TLEG",
					"discountedTotal": null,
					"maximal": null,
					"nationalHealthCareTotal": null,
					"netTotal": "5.77",
					"selected": false,
					"taxesTotal": "1.23",
					"total": "7.00"
				}
			]
		}
	]
}
 */
