package it.yolo.quote;

import javax.enterprise.context.ApplicationScoped;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;

import it.yolo.exception.InternalQuoteException;
import it.yolo.exception.QuotatorException;

@ApplicationScoped
public class PacketDurationPrice implements Quotator {

    private static final String CURRENCY_SYMBOL = "€";
    private static final String PACKET_DURATION_PATH = "/data/packet/data/packet_duration";
    private static final String PAYMENT_FREQUENCY_PATH = "/data/orderItem/0/instance/paymentFrequency";

    @Override
    public JsonNode quote(QuotatorDto quotatorDto, String token, String ordeCode, String productCode) throws InternalQuoteException, QuotatorException {
        // Extract packet duration data
        JsonNode packetDuration = extractPacketDuration(quotatorDto);
        JsonNode durationTypes = extractDurationTypes(packetDuration);
        String selectedDurationType = extractSelectedDurationType(quotatorDto);
        
        // Create response structure
        ArrayNode responseArray = JsonNodeFactory.instance.arrayNode(1);
        ObjectNode mainObject = JsonNodeFactory.instance.objectNode();
        
        // Process split payments
        ArrayNode splitPaymentsArray = createSplitPaymentsArray(durationTypes);
        mainObject.set("splitPayments", splitPaymentsArray);
        
        // Find appropriate duration to use
        JsonNode durationToUse = findDurationToUse(durationTypes, selectedDurationType);
        
        // Set pricing information
        setPricingInfo(mainObject, durationToUse);
        
        // Set other required fields
        setRequiredFields(mainObject);
        
        // Add the main object to the response array
        responseArray.add(mainObject);
        
        return responseArray;
    }
    
    /**
     * Extracts packet duration data from the quotator DTO
     */
    private JsonNode extractPacketDuration(QuotatorDto quotatorDto) throws QuotatorException {
        JsonNode packetDuration = quotatorDto.getOrderResponse().at(PACKET_DURATION_PATH);
        if (packetDuration == null || packetDuration.isMissingNode()) {
            throw new QuotatorException("packet_duration data is missing");
        }
        return packetDuration;
    }
    
    /**
     * Extracts duration types from packet duration data
     */
    private JsonNode extractDurationTypes(JsonNode packetDuration) throws QuotatorException {
        JsonNode durationTypes = packetDuration.get("packet_duration");
        if (durationTypes == null || !durationTypes.isArray() || durationTypes.isEmpty()) {
            throw new QuotatorException("packet_duration types are missing or invalid");
        }
        return durationTypes;
    }
    
    /**
     * Extracts the selected duration type from the quotator DTO
     */
    private String extractSelectedDurationType(QuotatorDto quotatorDto) {
        JsonNode selectedDurationTypeNode = quotatorDto.getOrderResponse().at(PAYMENT_FREQUENCY_PATH);
        return selectedDurationTypeNode.isNull() ? "" : selectedDurationTypeNode.asText();
    }
    
    /**
     * Creates the split payments array with all packet_duration items
     */
    private ArrayNode createSplitPaymentsArray(JsonNode durationTypes) throws QuotatorException {
        ArrayNode splitPaymentsArray = JsonNodeFactory.instance.arrayNode();
        for (JsonNode duration : durationTypes) {
            validateDuration(duration);
            splitPaymentsArray.add(createSplitPaymentObject(duration));
        }
        return splitPaymentsArray;
    }
    
    /**
     * Validates that a duration node has the required fields
     */
    private void validateDuration(JsonNode duration) throws QuotatorException {
        if (duration == null || !duration.has("description") || !duration.has("price")) {
            throw new QuotatorException("Invalid duration data structure");
        }
    }
    
    /**
     * Finds the appropriate duration to use based on selection or default
     */
    private JsonNode findDurationToUse(JsonNode durationTypes, String selectedDurationType) throws QuotatorException {
        JsonNode selectedDuration = null;
        JsonNode defaultDuration = null;
        
        // Find the selected duration and default duration
        for (JsonNode duration : durationTypes) {
            // Check for default duration
            if (duration.has("default") && duration.get("default").asBoolean()) {
                defaultDuration = duration;
            }
            
            // Check for selected duration matching paymentFrequency
            if (!selectedDurationType.isEmpty() && 
                duration.has("description") && 
                duration.get("description").asText().equals(selectedDurationType)) {
                selectedDuration = duration;
            }
        }
        
        // Determine which duration to use
        if (selectedDurationType.equalsIgnoreCase("null") || selectedDurationType.isEmpty()) {
            // Use default duration when paymentFrequency is null or empty
            return defaultDuration;
        } else {
            // Use selected duration when paymentFrequency matches
            if (selectedDuration == null) {
                throw new QuotatorException("Selected duration type not found: " + selectedDurationType);
            }
            return selectedDuration;
        }
    }
    
    /**
     * Sets pricing information in the main object
     */
    private void setPricingInfo(ObjectNode mainObject, JsonNode durationToUse) {
        mainObject.put("currency", CURRENCY_SYMBOL);
        mainObject.put("total", durationToUse.get("price").asDouble());
        
        // Set discountedTotal if available, otherwise null
        if (durationToUse.has("discountedPrice") && !durationToUse.get("discountedPrice").isNull()) {
            mainObject.put("discountedTotal", durationToUse.get("discountedPrice").asDouble());
        } else {
            mainObject.putNull("discountedTotal");
        }
    }
    
    /**
     * Sets other required fields in the main object
     */
    private void setRequiredFields(ObjectNode mainObject) {
        mainObject.putNull("nationalHealthCareTotal");
        mainObject.putNull("netTotal");
        mainObject.set("providerResponse", JsonNodeFactory.instance.objectNode());
        mainObject.putNull("taxesTotal");
        mainObject.putNull("warranties");
    }
    
    /**
     * Creates a splitPayment object from the packet_duration data
     * 
     * @param duration The JsonNode containing packet duration data
     * @return An ObjectNode formatted as a splitPayment object
     */
    private ObjectNode createSplitPaymentObject(JsonNode duration) {
        ObjectNode splitPaymentObj = JsonNodeFactory.instance.objectNode();
        
        // Copy all fields from duration node to maintain exact structure
        duration.fields().forEachRemaining(entry -> {
            String fieldName = entry.getKey();
            JsonNode fieldValue = entry.getValue();
            
            // Add the field with its original type
            splitPaymentObj.set(fieldName, fieldValue);
        });
        
        return splitPaymentObj;
    }
}