package it.yolo.quote;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.PGClientV2;
import it.yolo.client.request.PgQuotationData;
import it.yolo.client.request.PgQuotationRequest;
import it.yolo.exception.ClientException;
import it.yolo.exception.QuotatorException;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.Response;

@RequestScoped
public class External implements Quotator{

    @Inject
    Logger logger;


    @Inject
    @RestClient
    PGClientV2 pgClient;

    @ConfigProperty(name = "tenant.name")
    String tenant;

    @Override
    public JsonNode quote(QuotatorDto quotatorDto, String token, String ordeCode, String productCode) throws QuotatorException {

        logger.info("External.quote start + " + quotatorDto);

        PgQuotationData data=new PgQuotationData(quotatorDto.getCustomer(),
                quotatorDto.getOrderResponse(),
                quotatorDto.getProductResponse());
        PgQuotationRequest requestPg=new PgQuotationRequest();
        requestPg.setData(data);
        requestPg.setTenant(tenant);
        String startDate=requestPg.getData().getOrder().get("data").get("orderItem").get(0).get("start_date").asText();
        startDate=startDate.contains(".") ? startDate : startDate+".000";
        String expiresDate=requestPg.getData().getOrder().get("data").get("orderItem").get(0).get("expiration_date").asText();
        expiresDate = expiresDate.contains(".") ? expiresDate : expiresDate+".000";
        ((ObjectNode)requestPg.getData().getOrder().get("data").get("orderItem").get(0)).remove("start_date");
        ((ObjectNode)requestPg.getData().getOrder().get("data").get("orderItem").get(0)).remove("expiration_date");
        ((ObjectNode)requestPg.getData().getOrder().get("data").get("orderItem").get(0)).put("start_date", startDate);
        ((ObjectNode)requestPg.getData().getOrder().get("data").get("orderItem").get(0)).put("expiration_date", expiresDate);
        try{
            logger.info("External.quote call quote + " + requestPg);
            Response res= pgClient.quote(requestPg);
            JsonNode quoteResponse= res.readEntity(JsonNode.class);
            logger.info("External.quote response end + " + quoteResponse); 
            return  quoteResponse;
        }catch(WebApplicationException exception){
            logger.error("External.quote error + " + exception.getMessage());
            String errorResponse = exception.getResponse().readEntity(String.class);
            throw new QuotatorException(errorResponse);
        }
    }
}
