package it.yolo.quote;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.response.ProductResponse;

public class QuotatorDto {

    @JsonProperty("product")
    private ProductResponse productResponse;
    @JsonProperty("order")
    private JsonNode orderResponse;
    @JsonProperty("customer")
    private JsonNode customer;

    @JsonProperty("product")
    public ProductResponse getProductResponse() {
        return productResponse;
    }
    @JsonProperty("product")
    public void setProductResponse(ProductResponse productResponse) {
        this.productResponse = productResponse;
    }
    @JsonProperty("order")
    public JsonNode getOrderResponse() {
        return orderResponse;
    }
    @JsonProperty("order")
    public void setOrderResponse(JsonNode orderResponse) {
        this.orderResponse = orderResponse;
    }
    @JsonProperty("customer")
    public JsonNode getCustomer() {
        return customer;
    }
    @JsonProperty("customer")
    public void setCustomer(JsonNode customer) {
        this.customer = customer;
    }

    @Override
    public String toString() {
        return "QuotatorDto{" +
                "productResponse=" + productResponse +
                ", orderResponse=" + orderResponse +
                ", customer=" + customer +
                '}';
    }
}
