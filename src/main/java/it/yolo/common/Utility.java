package it.yolo.common;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Utility {

    public static Date parseDateFromString(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        try {
            return sdf.parse(date);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return null;
        }
    }

    public static Double calculatePercentage(String percentage, Double price) {
        Double percentageInt = Double.valueOf(percentage.replace("%", ""));
        Double finalPrice = price-(price * percentageInt / 100);
        return Double.parseDouble(String.format("%.2f", finalPrice)) ;
    }

    public static Double calculateFreeTrial(Double price) {
        Double finalPrice = price-price;
        return finalPrice;
    }

}
