package it.yolo.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import it.yolo.entity.WarrantyPremium;
import it.yolo.model.WarrantyDetail;

import javax.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@ApplicationScoped
public class WarrantyPremiumRepository implements PanacheRepository<WarrantyPremium> {

    /**
     * Find warranty premiums by warranty IDs, benefit amount and duration in months
     * 
     * @param warrantyIds List of warranty IDs to search for
     * @param benefitAmount The benefit amount
     * @param durationMonths Duration in months
     * @return List of matching warranty premiums
     */
    public List<WarrantyPremium> findByWarrantyIdsAndBenefitAmountAndDurationMonths(
            List<Integer> warrantyIds, 
            String benefitAmount, 
            Integer durationMonths) {
        
        return find("warrantyId in ?1 and benefitAmount = ?2 and durationMonths = ?3", 
                   warrantyIds, benefitAmount, durationMonths).list();
    }

    /**
     * Find warranty premium by single warranty ID, benefit amount and duration in months
     * 
     * @param warrantyId The warranty ID
     * @param benefitAmount The benefit amount
     * @param durationMonths Duration in months
     * @return WarrantyPremium or null if not found
     */
    public WarrantyPremium findByWarrantyIdAndBenefitAmountAndDurationMonths(
            Integer warrantyId, 
            String benefitAmount, 
            Integer durationMonths) {
        
        LocalDate today = LocalDate.now();

        return find("warrantyId = ?1 and benefitAmount = ?2 and durationMonths = ?3 " +
                  "and validFrom <= ?4 and (validTo is null or validTo >= ?4)",
                   warrantyId, benefitAmount, durationMonths, today)
                .firstResult();
    }

    /**
     * Find warranty premiums for a list of warranty details, each with its own benefit amount
     *
     * @param warrantyDetails List of warranty details with ID and benefit amounts
     * @param durationMonths Duration in months
     * @return List of matching warranty premiums
     */
    public List<WarrantyPremium> findPremiumsForWarranties(
            List<WarrantyDetail> warrantyDetails,
            Integer durationMonths) {
        
        List<WarrantyPremium> result = new ArrayList<>();
        LocalDate today = LocalDate.now();

        // Cerchiamo ciascun premio individualmente poiché ogni garanzia ha il proprio benefitAmount
        for (WarrantyDetail detail : warrantyDetails) {
            WarrantyPremium premium = find("warrantyId = ?1 and benefitAmount = ?2 and durationMonths = ?3 " +
                                         "and validFrom <= ?4 and (validTo is null or validTo >= ?4)",
                                        detail.getId(), detail.getBenefitAmount(), durationMonths, today)
                                     .firstResult();

            if (premium != null) {
                result.add(premium);
            }
        }

        return result;
    }

    /**
     * Find missing warranty details that don't have premiums for given duration
     *
     * @param warrantyDetails List of warranty details to check
     * @param durationMonths Duration in months
     * @return List of warranty details that don't have premiums
     */
    public List<WarrantyDetail> findMissingWarrantyDetails(
            List<WarrantyDetail> warrantyDetails,
            Integer durationMonths) {
        
        List<WarrantyDetail> missingDetails = new ArrayList<>();
        LocalDate today = LocalDate.now();

        for (WarrantyDetail detail : warrantyDetails) {
            long count = count("warrantyId = ?1 and benefitAmount = ?2 and durationMonths = ?3 " +
                             "and validFrom <= ?4 and (validTo is null or validTo >= ?4)",
                             detail.getId(), detail.getBenefitAmount(), durationMonths, today);

            if (count == 0) {
                missingDetails.add(detail);
            }
        }

        return missingDetails;
    }
}
