package it.yolo.client;

import javax.ws.rs.POST;
import javax.ws.rs.Path;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import com.fasterxml.jackson.databind.JsonNode;

@RegisterRestClient(configKey = "adapter")
public interface AdapterClient {

    @POST
    @Path("/pg/quote/fromPg")
    JsonNode fromPg(JsonNode req);

    @POST
    @Path("/pg/quote/toPg")
    JsonNode toPg(JsonNode req);

}
