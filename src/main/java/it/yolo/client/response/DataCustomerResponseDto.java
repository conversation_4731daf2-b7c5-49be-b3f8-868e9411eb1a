package it.yolo.client.response;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "customer_code",
        "external_code",
        "username",
        "name",
        "surname",
        "date_of_birth",
        "birth_city",
        "birth_province",
        "tax_code",
        "gender",
        "street",
        "street_number",
        "city",
        "country",
        "zip_code",
        "province",
        "primary_mail",
        "secondary_mail",
        "primary_phone",
        "secondary_phone",
        "language",
        "legal_form",
        "createdAt",
        "updatedAt"
})
public class DataCustomerResponseDto {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("customer_code")
    private String customerCode;
    @JsonProperty("external_code")
    private JsonNode externalCode;
    @JsonProperty("username")
    private Object username;
    @JsonProperty("name")
    private String name;
    @JsonProperty("surname")
    private String surname;
    @JsonProperty("date_of_birth")
    private String dateOfBirth;
    @JsonProperty("birth_city")
    private String birthCity;

    @JsonProperty("birth_country")
    private String birth_country;

    @JsonProperty("birth_state")
    private String birth_state;

    @JsonProperty("education")
    private String education;

    @JsonProperty("country_id")
    private String country_id;

    @JsonProperty("state_id")
    private String state_id;

    @JsonProperty("ndg")
    private String ndg;

    @JsonProperty("state")
    private String state;

    @JsonProperty("city_id")
    private String city_id;

    @JsonProperty("birth_state_id")
    private String birth_state_id;

    @JsonProperty("birth_city_id")
    private String birth_city_id;

    @JsonProperty("birth_country_id")
    private String birth_country_id;


    @JsonProperty("birth_state_abbr")
    private String birth_state_abbr;

    @JsonProperty("birth_province")
    private String birthProvince;
    @JsonProperty("tax_code")
    private String taxCode;
    @JsonProperty("gender")
    private String gender;
    @JsonProperty("street")
    private String street;
    @JsonProperty("street_number")
    private String streetNumber;
    @JsonProperty("city")
    private String city;
    @JsonProperty("country")
    private String country;
    @JsonProperty("zip_code")
    private String zipCode;
    @JsonProperty("province")
    private String province;
    @JsonProperty("primary_mail")
    private String primaryMail;
    @JsonProperty("secondary_mail")
    private String secondaryMail;
    @JsonProperty("primary_phone")
    private String primaryPhone;
    @JsonProperty("secondary_phone")
    private Object secondaryPhone;
    @JsonProperty("language")
    private String language;
    @JsonProperty("legal_form")
    private String legalForm;
    @JsonProperty("createdAt")
    private String createdAt;
    @JsonProperty("updatedAt")
    private String updatedAt;
    @JsonProperty("state_abbr")
    private String stateAbbr;
    @JsonProperty("password")
    private String password;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("customer_code")
    public String getCustomerCode() {
        return customerCode;
    }

    @JsonProperty("customer_code")
    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    @JsonProperty("external_code")
    public JsonNode getExternalCode() {
        return externalCode;
    }

    @JsonProperty("external_code")
    public void setExternalCode(JsonNode externalCode) {
        this.externalCode = externalCode;
    }

    @JsonProperty("username")
    public Object getUsername() {
        return username;
    }

    @JsonProperty("username")
    public void setUsername(Object username) {
        this.username = username;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("surname")
    public String getSurname() {
        return surname;
    }

    @JsonProperty("surname")
    public void setSurname(String surname) {
        this.surname = surname;
    }

    @JsonProperty("date_of_birth")
    public String getDateOfBirth() {
        return dateOfBirth;
    }

    @JsonProperty("date_of_birth")
    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    @JsonProperty("birth_city")
    public String getBirthCity() {
        return birthCity;
    }

    @JsonProperty("birth_city")
    public void setBirthCity(String birthCity) {
        this.birthCity = birthCity;
    }

    @JsonProperty("birth_province")
    public String getBirthProvince() {
        return birthProvince;
    }

    @JsonProperty("birth_province")
    public void setBirthProvince(String birthProvince) {
        this.birthProvince = birthProvince;
    }

    @JsonProperty("tax_code")
    public String getTaxCode() {
        return taxCode;
    }

    @JsonProperty("tax_code")
    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    @JsonProperty("gender")
    public String getGender() {
        return gender;
    }

    @JsonProperty("gender")
    public void setGender(String gender) {
        this.gender = gender;
    }

    @JsonProperty("street")
    public String getStreet() {
        return street;
    }

    @JsonProperty("street")
    public void setStreet(String street) {
        this.street = street;
    }

    @JsonProperty("street_number")
    public String getStreetNumber() {
        return streetNumber;
    }

    @JsonProperty("street_number")
    public void setStreetNumber(String streetNumber) {
        this.streetNumber = streetNumber;
    }

    @JsonProperty("city")
    public String getCity() {
        return city;
    }

    @JsonProperty("city")
    public void setCity(String city) {
        this.city = city;
    }

    @JsonProperty("country")
    public String getCountry() {
        return country;
    }

    @JsonProperty("country")
    public void setCountry(String country) {
        this.country = country;
    }

    @JsonProperty("zip_code")
    public String getZipCode() {
        return zipCode;
    }

    @JsonProperty("zip_code")
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    @JsonProperty("province")
    public String getProvince() {
        return province;
    }

    @JsonProperty("province")
    public void setProvince(String province) {
        this.province = province;
    }

    @JsonProperty("primary_mail")
    public String getPrimaryMail() {
        return primaryMail;
    }

    @JsonProperty("primary_mail")
    public void setPrimaryMail(String primaryMail) {
        this.primaryMail = primaryMail;
    }

    @JsonProperty("secondary_mail")
    public String getSecondaryMail() {
        return secondaryMail;
    }

    @JsonProperty("secondary_mail")
    public void setSecondaryMail(String secondaryMail) {
        this.secondaryMail = secondaryMail;
    }

    @JsonProperty("primary_phone")
    public String getPrimaryPhone() {
        return primaryPhone;
    }

    @JsonProperty("primary_phone")
    public void setPrimaryPhone(String primaryPhone) {
        this.primaryPhone = primaryPhone;
    }

    @JsonProperty("secondary_phone")
    public Object getSecondaryPhone() {
        return secondaryPhone;
    }

    @JsonProperty("secondary_phone")
    public void setSecondaryPhone(Object secondaryPhone) {
        this.secondaryPhone = secondaryPhone;
    }

    @JsonProperty("language")
    public String getLanguage() {
        return language;
    }

    @JsonProperty("language")
    public void setLanguage(String language) {
        this.language = language;
    }

    @JsonProperty("legal_form")
    public String getLegalForm() {
        return legalForm;
    }

    @JsonProperty("legal_form")
    public void setLegalForm(String legalForm) {
        this.legalForm = legalForm;
    }

    @JsonProperty("createdAt")
    public String getCreatedAt() {
        return createdAt;
    }

    @JsonProperty("createdAt")
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    @JsonProperty("updatedAt")
    public String getUpdatedAt() {
        return updatedAt;
    }

    @JsonProperty("updatedAt")
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @JsonProperty("password")
    public String getPassword() {
        return password;
    }

    @JsonProperty("password")
    public void setPassword(String password) {
        this.password = password;
    }

    @JsonProperty("state_abbr")
    public String getStateAbbr() {
        return stateAbbr;
    }

    @JsonProperty("state_abbr")
    public void setStateAbbr(String stateAbbr) {
        this.stateAbbr = stateAbbr;
    }


    public String getBirth_country() {
        return birth_country;
    }

    public void setBirth_country(String birth_country) {
        this.birth_country = birth_country;
    }

    public String getBirth_state() {
        return birth_state;
    }

    public void setBirth_state(String birth_state) {
        this.birth_state = birth_state;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getCountry_id() {
        return country_id;
    }

    public void setCountry_id(String country_id) {
        this.country_id = country_id;
    }

    public String getState_id() {
        return state_id;
    }

    public void setState_id(String state_id) {
        this.state_id = state_id;
    }

    public String getNdg() {
        return ndg;
    }

    public void setNdg(String ndg) {
        this.ndg = ndg;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity_id() {
        return city_id;
    }

    public void setCity_id(String city_id) {
        this.city_id = city_id;
    }

    public String getBirth_state_id() {
        return birth_state_id;
    }

    public void setBirth_state_id(String birth_state_id) {
        this.birth_state_id = birth_state_id;
    }

    public String getBirth_city_id() {
        return birth_city_id;
    }

    public void setBirth_city_id(String birth_city_id) {
        this.birth_city_id = birth_city_id;
    }

    public String getBirth_country_id() {
        return birth_country_id;
    }

    public void setBirth_country_id(String birth_country_id) {
        this.birth_country_id = birth_country_id;
    }

    public String getBirth_state_abbr() {
        return birth_state_abbr;
    }

    public void setBirth_state_abbr(String birth_state_abbr) {
        this.birth_state_abbr = birth_state_abbr;
    }
}
