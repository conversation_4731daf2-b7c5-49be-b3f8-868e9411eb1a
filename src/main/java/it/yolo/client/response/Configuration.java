package it.yolo.client.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

public class Configuration {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("emission")
    private String emission;

    @JsonProperty("quotation")
    private String quotation;

    @JsonProperty("emissionPrefix")
    private String emissionPrefix;

    @JsonProperty("certificate")
    private String certificate;

    @JsonProperty("claimProvider")
    private String claimProvider;

    @JsonProperty("claimType")
    private String claimType;

    @JsonProperty("deactivationProvider")
    private String deactivationProvider;

    @JsonProperty("deactivationType")
    private String deactivationType;

    @JsonProperty("withdrawType")
    private String withdrawType;

    @JsonProperty("canOpenClaim")
    private Boolean canOpenClaim;

    @JsonProperty("properties")
    private JsonNode properties;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("emission")
    public String getEmission() {
        return emission;
    }

    @JsonProperty("emission")
    public void setEmission(String emission) {
        this.emission = emission;
    }

    @JsonProperty("emissionPrefix")
    public String getEmissionPrefix() {
        return emissionPrefix;
    }

    @JsonProperty("emissionPrefix")
    public void setEmissionPrefix(String emissionPrefix) {
        this.emissionPrefix = emissionPrefix;
    }

    @JsonProperty("certificate")
    public String getCertificate() {
        return certificate;
    }

    @JsonProperty("certificate")
    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    @JsonProperty("quotation")
    public String getQuotation() {
        return quotation;
    }

    @JsonProperty("quotation")
    public void setQuotation(String quotation) {
        this.quotation = quotation;
    }

    @JsonProperty("canOpenClaim")
    public Boolean getCanOpenClaim() {
        return canOpenClaim;
    }

    @JsonProperty("canOpenClaim")
    public void setCanOpenClaim(Boolean canOpenClaim) {
        this.canOpenClaim = canOpenClaim;
    }

    @JsonProperty("claimProvider")
    public String getClaimProvider() {
        return claimProvider;
    }

    @JsonProperty("claimProvider")
    public void setClaimProvider(String claimProvider) {
        this.claimProvider = claimProvider;
    }

    @JsonProperty("claimType")
    public String getClaimType() {
        return claimType;
    }

    @JsonProperty("claimType")
    public void setClaimType(String claimType) {
        this.claimType = claimType;
    }

    @JsonProperty("deactivationProvider")
    public String getDeactivationProvider() {
        return deactivationProvider;
    }

    @JsonProperty("deactivationProvider")
    public void setDeactivationProvider(String deactivationProvider) {
        this.deactivationProvider = deactivationProvider;
    }

    @JsonProperty("deactivationType")
    public String getDeactivationType() {
        return deactivationType;
    }

    @JsonProperty("deactivationType")
    public void setDeactivationType(String deactivationType) {
        this.deactivationType = deactivationType;
    }


    @JsonProperty("withdrawType")
    public String getWithdrawType() {
        return withdrawType;
    }


    @JsonProperty("withdrawType")
    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    @JsonProperty("properties")
    public JsonNode getProperties() {
        return properties;
    }

    @JsonProperty("properties")
    public void setProperties(JsonNode properties) {
        this.properties = properties;
    }
}
