package it.yolo.client.response;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "code",
        "description",
        "startDate",
        "recurring",
        "splits",
        "questions",
        "categories",
        "asset",
        "insuranceCompany"
})
public class ProductOrderResponse {

    @JsonProperty("data")
    private DataProductOrderResponse dataProductOrderResponse;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    public DataProductOrderResponse getDataProduct() {
        return dataProductOrderResponse;
    }

    public void setDataProduct(DataProductOrderResponse dataProductOrderResponse) {
        this.dataProductOrderResponse = dataProductOrderResponse;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
