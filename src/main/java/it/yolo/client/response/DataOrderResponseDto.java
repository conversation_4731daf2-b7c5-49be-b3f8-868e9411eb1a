package it.yolo.client.response;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.dto.DataPacketResponse;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataOrderResponseDto {

    @JsonProperty("id")
    private Long id;
    @JsonProperty("orderCode")
    private String orderCode;

    @JsonProperty("productId")
    private Long productId;

    @JsonProperty("orderHistory")
    private JsonNode orderHistory;
    @JsonProperty("packetId")
    private Integer packetId;
    @JsonProperty("anagStates")
    private AnagStates anagStates;

    @JsonProperty("customerId")
    private Integer customerId;
    @JsonProperty("policyCode")
    private Object policyCode;
    @JsonProperty("choosenProperties")
    private Object choosenProperties;
    @JsonProperty("createdBy")
    private String createdBy;
    @JsonProperty("updatedBy")
    private String updatedBy;
    @JsonProperty("packet")
    private DataPacketResponse packet;
    @JsonProperty("fieldToRecover")
    private JsonNode fieldToRecover;
    @JsonProperty("start_date")
    private LocalDateTime start_date;
    @JsonProperty("expiration_date")
    private LocalDateTime expiration_date;
    @JsonProperty("brokerId")
    private Integer brokerId;
    @JsonProperty("insurancePremium")
    private Integer insurancePremium;
    @JsonProperty("companyId")
    private Integer companyId;
    @JsonProperty("createdAt")
    private String createdAt;
    @JsonProperty("paymentToken")
    private String paymentToken;
    @JsonProperty("paymentTransactionId")
    private String paymentTransactionId;
    @JsonProperty("paymentType")
    private String paymentType;
    @JsonProperty("productType")
    private String productType;
    @JsonProperty("stepState")
    private List<String> stepState;
    @JsonProperty("version")
    private String version;
    @JsonProperty("updatedAt")
    private String updatedAt;
    @JsonProperty("product")
    private ProductOrderResponse productOrderResponse;
    @JsonProperty("orderItem")
    private List<OrderItem> orderItem = null;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }


    @JsonProperty("id")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("policyCode")
    public Object getPolicyCode() {
        return policyCode;
    }

    @JsonProperty("policyCode")
    public void setPolicyCode(Object policyCode) {
        this.policyCode = policyCode;
    }

    @JsonProperty("anagStates")
    public AnagStates getAnagStates() {
        return anagStates;
    }

    @JsonProperty("anagStates")
    public void setAnagStates(AnagStates anagStates) {
        this.anagStates = anagStates;
    }

    @JsonProperty("packetId")
    public Integer getPacketId() {
        return packetId;
    }

    @JsonProperty("packetId")
    public void setPacketId(Integer packetId) {
        this.packetId = packetId;
    }



    @JsonProperty("customerId")
    public Integer getCustomerId() {
        return customerId;
    }

    @JsonProperty("customerId")
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @JsonProperty("choosenProperties")
    public Object getChoosenProperties() {
        return choosenProperties;
    }

    @JsonProperty("choosenProperties")
    public void setChoosenProperties(Object choosenProperties) {
        this.choosenProperties = choosenProperties;
    }


    @JsonProperty("createdBy")
    public String getCreatedBy() {
        return createdBy;
    }

    @JsonProperty("createdBy")
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @JsonProperty("updatedBy")
    public String getUpdatedBy() {
        return updatedBy;
    }

    @JsonProperty("updatedBy")
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @JsonProperty("createdAt")
    public String getCreatedAt() {
        return createdAt;
    }

    @JsonProperty("createdAt")
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    @JsonProperty("updatedAt")
    public String getUpdatedAt() {
        return updatedAt;
    }

    @JsonProperty("updatedAt")
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    @JsonProperty("orderItem")
    public List<OrderItem> getOrderItem() {
        return orderItem;
    }

    @JsonProperty("orderItem")
    public void setOrderItem(List<OrderItem> orderItem) {
        this.orderItem = orderItem;
    }

    @JsonProperty("productId")
    public Long getProductId() {
        return productId;
    }

    @JsonProperty("productId")
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();


    @JsonProperty("start_date")
    public LocalDateTime getStart_date() {
        return start_date;
    }
    @JsonProperty("start_date")
    public void setStart_date(LocalDateTime start_date) {
        this.start_date = start_date;
    }

    @JsonProperty("expiration_date")
    public LocalDateTime getExpiration_date() {
        return expiration_date;
    }

    @JsonProperty("expiration_date")
    public void setExpiration_date(LocalDateTime expiration_date) {
        this.expiration_date = expiration_date;
    }

    @JsonProperty("brokerId")
    public Integer getBrokerId() {
        return brokerId;
    }

    @JsonProperty("brokerId")
    public void setBrokerId(Integer brokerId) {
        this.brokerId = brokerId;
    }

    @JsonProperty("packet")
    public DataPacketResponse getPacket() {
        return packet;
    }

    @JsonProperty("packet")
    public void setPacket(DataPacketResponse packet) {
        this.packet = packet;
    }

    @JsonProperty("companyId")
    public Integer getCompanyId() {
        return companyId;
    }

    @JsonProperty("companyId")
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    @JsonProperty("insurancePremium")
    public Integer getInsurancePremium() {
        return insurancePremium;
    }

    @JsonProperty("insurancePremium")
    public void setInsurancePremium(Integer insurancePremium) {
        this.insurancePremium = insurancePremium;
    }

    @JsonProperty("paymentToken")
    public String getPaymentToken() {
        return paymentToken;
    }

    @JsonProperty("paymentToken")
    public void setPaymentToken(String paymentToken) {
        this.paymentToken = paymentToken;
    }

    @JsonProperty("paymentTransactionId")
    public String getPaymentTransactionId() {
        return paymentTransactionId;
    }

    @JsonProperty("paymentTransactionId")
    public void setPaymentTransactionId(String paymentTransactionId) {
        this.paymentTransactionId = paymentTransactionId;
    }

    @JsonProperty("paymentType")
    public String getPaymentType() {
        return paymentType;
    }

    @JsonProperty("paymentType")
    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    @JsonProperty("stepState")
    public List<String> getStepState() {
        return stepState;
    }

    @JsonProperty("stepState")
    public void setStepState(List<String> stepState) {
        this.stepState = stepState;
    }

    @JsonProperty("version")
    public String getVersion() {
        return version;
    }

    @JsonProperty("version")
    public void setVersion(String version) {
        this.version = version;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperties(Map<String, Object> additionalProperties) {
        this.additionalProperties = additionalProperties;
    }

    @JsonProperty("fieldToRecover")
    public JsonNode getFieldToRecover() {
        return fieldToRecover;
    }
    @JsonProperty("fieldToRecover")
    public void setFieldToRecover(JsonNode fieldToRecover) {
        this.fieldToRecover = fieldToRecover;
    }

    @JsonProperty("productType")
    public String getProductType() {
        return productType;
    }

    @JsonProperty("productType")
    public void setProductType(String productType) {
        this.productType = productType;
    }

    @JsonProperty("product")
    public ProductOrderResponse getProductOrderResponse() {
        return productOrderResponse;
    }

    @JsonProperty("product")
    public void setProductOrderResponse(ProductOrderResponse productOrderResponse) {
        this.productOrderResponse = productOrderResponse;
    }

    @JsonProperty("orderHistory")
    public JsonNode getOrderHistory() {
        return orderHistory;
    }

    @JsonProperty("orderHistory")
    public void setOrderHistory(JsonNode orderHistory) {
        this.orderHistory = orderHistory;
    }
/*
    //@JsonProperty("packet")
    //private Packet packet=new Packet();
    //@JsonProperty("orderHistory")
    //private OrderHistory orderHistory;
    //@JsonProperty("customer")
    //private DataCustomerResponseDto customer;
    // @JsonProperty("plan_id")
    //private String planId;
    //@JsonProperty("plan_name")
    //private String planName;
    //@JsonProperty("paymentToken")
    //private String paymentToken;
    //@JsonProperty("paymentTransactionId")
    //private String paymentTransactionId;
    //@JsonProperty("version")
    //private String version;
    //@JsonProperty("productType")
    //private String productType;
    //@JsonProperty("paymentType")
    //private String paymentType;
    //@JsonProperty("paymentProvider")
    //private String paymentProvider;
    //@JsonProperty("externalId")
     @JsonProperty("stepState")
    private List<String> stepState = null;



    @JsonProperty("stepState")
    public List<String> getStepState() {
        return stepState;
    }

    @JsonProperty("stepState")
    public void setStepState(List<String> stepState) {
        this.stepState = stepState;
    }
     @JsonProperty("type")
    private String type;
    @JsonProperty("packet")
    public Packet getPacket() {
        return packet;
    }

    @JsonProperty("packet")
    public void setPacket(Packet packet) {
        this.packet = packet;
    }

    @JsonProperty("orderHistory")
    public OrderHistory getOrderHistory() {
        return orderHistory;
    }

    @JsonProperty("orderHistory")
    public void setOrderHistory(OrderHistory orderHistory) {
        this.orderHistory = orderHistory;
    }

    //private String externalId;
         public DataCustomerResponseDto getCustomer() {
        return customer;
    }

    public void setCustomer(DataCustomerResponseDto customer) {
        this.customer = customer;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    @JsonProperty("paymentToken")
    public String getPaymentToken() {
        return paymentToken;
    }

    @JsonProperty("paymentToken")
    public void setPaymentToken(String paymentToken) {
        this.paymentToken = paymentToken;
    }

    @JsonProperty("paymentTransactionId")
    public String getPaymentTransactionId() {
        return paymentTransactionId;
    }

    @JsonProperty("paymentTransactionId")
    public void setPaymentTransactionId(String paymentTransactionId) {
        this.paymentTransactionId = paymentTransactionId;
    }



         @JsonProperty("productType")
    public String getProductType() {
        return productType;
    }

    @JsonProperty("productType")
    public void setProductType(String productType) {
        this.productType = productType;
    }


    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
    @JsonProperty("paymentFrequency")
    public String getPaymentType() {
        return paymentType;
    }

    @JsonProperty("paymentFrequency")
    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    @JsonProperty("paymentProvider")
    public String getPaymentProvider() {
        return paymentProvider;
    }

     @JsonProperty("paymentProvider")
    public void setPaymentProvider(String paymentProvider) {
        this.paymentProvider = paymentProvider;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }


    @JsonProperty("product")
    public ProductOrderResponse getProduct() {
        return productOrderResponse;
    }

    @JsonProperty("product")
    public void setProduct(ProductOrderResponse productOrderResponse) {
        this.productOrderResponse = productOrderResponse;
    }

     @JsonProperty("insurancePremium")
    public Double getInsurancePremium() {
        return insurancePremium;
    }

    @JsonProperty("insurancePremium")
    public void setInsurancePremium(Double insurancePremium) {
        this.insurancePremium = insurancePremium;
    }

     */


}
