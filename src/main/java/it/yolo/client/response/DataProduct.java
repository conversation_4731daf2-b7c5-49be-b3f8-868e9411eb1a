
package it.yolo.client.response;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonPropertyOrder({
        "id",
        "code",
        "description",
        "startDate",
        "endDate",
        "recurring",
        "externalId",
        "splits",
        "asset",
        "insuranceCompany",
        "additionalProductInfo",
        "duration_type"
})
public class DataProduct {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("code")
    private String code;
    @JsonProperty("description")
    private String description;
    @JsonProperty("startDate")
    private String startDate;
    @JsonProperty("price")
    private Double price;
    @JsonProperty("endDate")
    private String endDate;
    @JsonProperty("recurring")
    private Boolean recurring;
    @JsonProperty("externalId")
    private String externalId;
    @JsonProperty("splits")
    private List<Object> splits = null;
    @JsonProperty("properties")
    private JsonNode properties;
    @JsonProperty("insuranceCompany")
    private String insuranceCompany;
    @JsonProperty("duration")
    private Integer duration;
    @JsonProperty("duration_type")
    private String durationType;
    @JsonProperty("configuration")
    private Configuration configuration;
    @JsonProperty("additionalProductInfo")
    private JsonNode additionalProductInfo;
    @JsonProperty("productType")
    private String productType;
    @JsonProperty("quotation")
    private String quotation;
    @JsonProperty("legacy")
    private JsonNode legacy;
    @JsonProperty("packet")
    private PacketResponse packet;

    public PacketResponse getPacket() {
        return packet;
    }

    public void setPacket(PacketResponse packet) {
        this.packet = packet;
    }

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("code")
    public String getCode() {
        return code;
    }

    @JsonProperty("code")
    public void setCode(String code) {
        this.code = code;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("startDate")
    public String getStartDate() {
        return startDate;
    }

    @JsonProperty("startDate")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("endDate")
    public String getEndDate() {
        return endDate;
    }

    @JsonProperty("endDate")
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @JsonProperty("price")
    public Double getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Double price) {
        this.price = price;
    }

    @JsonProperty("recurring")
    public Boolean getRecurring() {
        return recurring;
    }

    @JsonProperty("recurring")
    public void setRecurring(Boolean recurring) {
        this.recurring = recurring;
    }

    @JsonProperty("externalId")
    public Object getExternalId() {
        return externalId;
    }

    @JsonProperty("externalId")
    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    @JsonProperty("splits")
    public List<Object> getSplits() {
        return splits;
    }

    @JsonProperty("splits")
    public void setSplits(List<Object> splits) {
        this.splits = splits;
    }

    @JsonProperty("properties")
    public JsonNode getProperties() {
        return properties;
    }

    @JsonProperty("properties")
    public void setProperties(JsonNode properties) {
        this.properties = properties;
    }

    @JsonProperty("insuranceCompany")
    public String getInsuranceCompany() {
        return insuranceCompany;
    }

    @JsonProperty("insuranceCompany")
    public void setInsuranceCompany(String insuranceCompany) {
        this.insuranceCompany = insuranceCompany;
    }

    @JsonProperty("duration")
    public Integer getDuration() {
        return duration;
    }

    @JsonProperty("duration")
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @JsonProperty("duration_type")
    public String getDurationType() {
        return durationType;
    }

    @JsonProperty("duration_type")
    public void setDurationType(final String durationType) {
        this.durationType = durationType;
    }

    @JsonProperty("configuration")
    public Configuration getConfiguration() {
        return configuration;
    }

    @JsonProperty("configuration")
    public void setConfiguration(Configuration configuration) {
        this.configuration = configuration;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public JsonNode getLegacy() {
        return legacy;
    }

    public void setLegacy(JsonNode legacy) {
        this.legacy = legacy;
    }

    @JsonProperty("additionalProductInfo")
    public JsonNode getAdditionalProductInfo() {
        return additionalProductInfo;
    }

    @JsonProperty("additionalProductInfo")
    public void setAdditionalProductInfo(JsonNode additionalProductInfo) {
        this.additionalProductInfo = additionalProductInfo;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
