package it.yolo.client.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentMethod {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("externalId")
    private Integer externalId;

    @JsonProperty("paymentMethod")
    private String paymentMethod;

    @JsonProperty("provider")
    private String provider;

    @JsonProperty("paymentMethodType")
    private String paymentMethodType;

    @JsonProperty("type")
    private String type;

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("paymentMethod")
    public String getPaymentMethod() {
        return paymentMethod;
    }

    @JsonProperty("paymentMethod")
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    @JsonProperty("paymentMethodType")
    public String getPaymentMethodType() {
        return paymentMethodType;
    }

    @JsonProperty("paymentMethodType")
    public void setPaymentMethodType(String paymentMethodType) {
        this.paymentMethodType = paymentMethodType;
    }

    @JsonProperty("type")
    public String getType() {
        return type;
    }

    @JsonProperty("type")
    public void setType(String type) {
        this.type = type;
    }

    @JsonProperty("externalId")
    public Integer getExternalId() {
        return externalId;
    }

    @JsonProperty("externalId")
    public void setExternalId(Integer externalId) {
        this.externalId = externalId;
    }

    @JsonProperty("provider")
    public String getProvider() {
        return provider;
    }

    @JsonProperty("provider")
    public void setProvider(String provider) {
        this.provider = provider;
    }
}
