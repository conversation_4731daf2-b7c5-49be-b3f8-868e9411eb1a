package it.yolo.client.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

public class OrderPgDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("warrantiesUser")
    private JsonNode warrantiesUser;

    @JsonProperty("orderCode")
    private String orderCode;

    @JsonProperty("customerId")
    private Long customerId;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("warrantiesUser")
    public JsonNode getWarrantiesUser() {
        return warrantiesUser;
    }

    @JsonProperty("warrantiesUser")
    public void setWarrantiesUser(JsonNode warrantiesUser) {
        this.warrantiesUser = warrantiesUser;
    }

    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("customerId")
    public Long getCustomerId() {
        return customerId;
    }

    @JsonProperty("customerId")
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public OrderPgDto() {
    }

    public OrderPgDto(Long id, JsonNode warrantiesUser, String orderCode, Long customerId) {
        this.id = id;
        this.warrantiesUser = warrantiesUser;
        this.orderCode = orderCode;
        this.customerId = customerId;
    }
}
