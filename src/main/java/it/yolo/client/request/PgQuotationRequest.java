package it.yolo.client.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PgQuotationRequest {

    @JsonProperty("data")
    private PgQuotationData data;

    @JsonProperty("tenant")
    private String tenant;


    @JsonProperty("data")
    public PgQuotationData getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(PgQuotationData data) {
        this.data = data;
    }

    @JsonProperty("tenant")
    public String getTenant() {
        return tenant;
    }

    @JsonProperty("tenant")
    public void setTenant(String tenant) {
        this.tenant = tenant;
    }
}
