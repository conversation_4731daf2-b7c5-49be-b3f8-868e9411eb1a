package it.yolo.client.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.response.ProductResponse;


public class PgQuotationData {

    @JsonProperty("customer")
    private JsonNode customer;

    @JsonProperty("order")
    private JsonNode order;

    @JsonProperty("product")
    private ProductResponse product;

    @JsonProperty("customer")
    public JsonNode getCustomer() {
        return customer;
    }

    @JsonProperty("customer")
    public void setCustomer(JsonNode customer) {
        this.customer = customer;
    }

    @JsonProperty("order")
    public JsonNode getOrder() {
        return order;
    }

    @JsonProperty("order")
    public void setOrder(JsonNode order) {
        this.order = order;
    }

    @JsonProperty("product")
    public ProductResponse getProduct() {
        return product;
    }

    @JsonProperty("product")
    public void setProduct(ProductResponse product) {
        this.product = product;
    }

    public PgQuotationData(JsonNode customer, JsonNode order, ProductResponse product) {
        this.customer = customer;
        this.order = order;
        this.product = product;
    }

    public PgQuotationData() {
    }
}
