package it.yolo.client;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.response.OrderResponseDto;
import it.yolo.dto.OrderItemResponse;
import it.yolo.model.BaseData;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.util.List;

@Path("/v1/order")
@RegisterRestClient(configKey = "iad-order")
public interface OrderClient {

    @GET
    @Path("details/{orderItemId}")
    Response orderDetailsByOrderItemId(@HeaderParam("Authorization") String token, @PathParam("orderItemId") Long orderItemId);

    @PUT
    @Path("orderItem/price")
    Response updatePriceByOrderItemId(BaseData<List<OrderItemResponse>> orderItems, @HeaderParam("Authorization") String token);

    @GET
    @Path("{id}")
    OrderResponseDto findById(@PathParam("id") Long id, @HeaderParam("Authorization") String token);

    @PUT
    @Path("quotation/{order_code}")
    Response updateQuotation(@PathParam("order_code") String orderCode, JsonNode quotation, @HeaderParam("Authorization") String token);

}
