package it.yolo.client;

import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

@Path("/iad/v1/")
@RegisterRestClient(configKey = "coupon-url")
public interface CouponClient {

    @GET
    @Path("coupons/byCode/{couponCode}")
    Response getCouponResponse(@HeaderParam("Authorization") String token,@PathParam("couponCode") String couponCode,@QueryParam("insensitive") boolean insensitive);

    @GET
    @Path("promotionsOrders/validate/{couponCode}")
    Response getValidateCoupon(@HeaderParam("Authorization") String token,@PathParam("couponCode") String couponCode,@QueryParam("insensitive") boolean insensitive );
    
}
