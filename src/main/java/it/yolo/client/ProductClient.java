package it.yolo.client;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Response;

@Path("/v2/products")
@RegisterRestClient(configKey="iad-product")
public interface ProductClient {

    @GET
    @Path("{id}")
    Response findById(@HeaderParam("Authorization") String token, @PathParam("id")Long id);

}