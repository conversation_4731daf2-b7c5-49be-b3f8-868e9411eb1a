package it.yolo.client.product.response.dto;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "mini_url",
        "large_url",
        "small_url",
        "image_type",
        "product_url",
        "original_url"
})
public class ImageProduct {

    @JsonProperty("mini_url")
    private String miniUrl;
    @JsonProperty("large_url")
    private String largeUrl;
    @JsonProperty("small_url")
    private String smallUrl;
    @JsonProperty("image_type")
    private String imageType;
    @JsonProperty("product_url")
    private String productUrl;
    @JsonProperty("original_url")
    private String originalUrl;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("mini_url")
    public String getMiniUrl() {
        return miniUrl;
    }

    @JsonProperty("mini_url")
    public void setMiniUrl(String miniUrl) {
        this.miniUrl = miniUrl;
    }

    @JsonProperty("large_url")
    public String getLargeUrl() {
        return largeUrl;
    }

    @JsonProperty("large_url")
    public void setLargeUrl(String largeUrl) {
        this.largeUrl = largeUrl;
    }

    @JsonProperty("small_url")
    public String getSmallUrl() {
        return smallUrl;
    }

    @JsonProperty("small_url")
    public void setSmallUrl(String smallUrl) {
        this.smallUrl = smallUrl;
    }

    @JsonProperty("image_type")
    public String getImageType() {
        return imageType;
    }

    @JsonProperty("image_type")
    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    @JsonProperty("product_url")
    public String getProductUrl() {
        return productUrl;
    }

    @JsonProperty("product_url")
    public void setProductUrl(String productUrl) {
        this.productUrl = productUrl;
    }

    @JsonProperty("original_url")
    public String getOriginalUrl() {
        return originalUrl;
    }

    @JsonProperty("original_url")
    public void setOriginalUrl(String originalUrl) {
        this.originalUrl = originalUrl;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
