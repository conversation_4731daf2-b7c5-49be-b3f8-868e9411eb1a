package it.yolo.client.product.response.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import it.yolo.client.response.AnagStates;

import java.util.List;

public class DataResponseDto {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("orderCode")
    private String orderCode;
    @JsonProperty("policyCode")
    private Object policyCode;
    @JsonProperty("anagStates")
    private AnagStates anagStates;
    @JsonProperty("packetId")
    private Integer packetId;
    @JsonProperty("product")
    private Product product;
    @JsonProperty("brokerId")
    private Integer brokerId;
    @JsonProperty("companyId")
    private Integer companyId;
    @JsonProperty("customerId")
    private Integer customerId;
    @JsonProperty("choosenProperties")
    private Object choosenProperties;
    @JsonProperty("insurancePremium")
    private Double insurancePremium;
    @JsonProperty("createdBy")
    private String createdBy;
    @JsonProperty("updatedBy")
    private String updatedBy;
    @JsonProperty("createdAt")
    private String createdAt;
    @JsonProperty("updatedAt")
    private String updatedAt;
    @JsonProperty("packet")
    private Packet packet;
    @JsonProperty("orderHistory")
    private OrderHistory orderHistory;
    @JsonProperty("stepState")
    private List<String> stepState = null;
    @JsonProperty("orderItem")
    private List<OrderItem> orderItem = null;

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }

    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("policyCode")
    public Object getPolicyCode() {
        return policyCode;
    }

    @JsonProperty("policyCode")
    public void setPolicyCode(Object policyCode) {
        this.policyCode = policyCode;
    }

    @JsonProperty("anagStates")
    public AnagStates getAnagStates() {
        return anagStates;
    }

    @JsonProperty("anagStates")
    public void setAnagStates(AnagStates anagStates) {
        this.anagStates = anagStates;
    }

    @JsonProperty("packetId")
    public Integer getPacketId() {
        return packetId;
    }

    @JsonProperty("packetId")
    public void setPacketId(Integer packetId) {
        this.packetId = packetId;
    }

    @JsonProperty("product")
    public Product getProduct() {
        return product;
    }

    @JsonProperty("product")
    public void setProduct(Product product) {
        this.product = product;
    }

    @JsonProperty("brokerId")
    public Integer getBrokerId() {
        return brokerId;
    }

    @JsonProperty("brokerId")
    public void setBrokerId(Integer brokerId) {
        this.brokerId = brokerId;
    }

    @JsonProperty("companyId")
    public Integer getCompanyId() {
        return companyId;
    }

    @JsonProperty("companyId")
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    @JsonProperty("customerId")
    public Integer getCustomerId() {
        return customerId;
    }

    @JsonProperty("customerId")
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @JsonProperty("choosenProperties")
    public Object getChoosenProperties() {
        return choosenProperties;
    }

    @JsonProperty("choosenProperties")
    public void setChoosenProperties(Object choosenProperties) {
        this.choosenProperties = choosenProperties;
    }

    @JsonProperty("insurancePremium")
    public Double getInsurancePremium() {
        return insurancePremium;
    }

    @JsonProperty("insurancePremium")
    public void setInsurancePremium(Double insurancePremium) {
        this.insurancePremium = insurancePremium;
    }

    @JsonProperty("createdBy")
    public String getCreatedBy() {
        return createdBy;
    }

    @JsonProperty("createdBy")
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @JsonProperty("updatedBy")
    public String getUpdatedBy() {
        return updatedBy;
    }

    @JsonProperty("updatedBy")
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @JsonProperty("createdAt")
    public String getCreatedAt() {
        return createdAt;
    }

    @JsonProperty("createdAt")
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    @JsonProperty("updatedAt")
    public String getUpdatedAt() {
        return updatedAt;
    }

    @JsonProperty("updatedAt")
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    @JsonProperty("packet")
    public Packet getPacket() {
        return packet;
    }

    @JsonProperty("packet")
    public void setPacket(Packet packet) {
        this.packet = packet;
    }

    @JsonProperty("orderHistory")
    public OrderHistory getOrderHistory() {
        return orderHistory;
    }

    @JsonProperty("orderHistory")
    public void setOrderHistory(OrderHistory orderHistory) {
        this.orderHistory = orderHistory;
    }

    @JsonProperty("stepState")
    public List<String> getStepState() {
        return stepState;
    }

    @JsonProperty("stepState")
    public void setStepState(List<String> stepState) {
        this.stepState = stepState;
    }

    @JsonProperty("orderItem")
    public List<OrderItem> getOrderItem() {
        return orderItem;
    }

    @JsonProperty("orderItem")
    public void setOrderItem(List<OrderItem> orderItem) {
        this.orderItem = orderItem;
    }
}
