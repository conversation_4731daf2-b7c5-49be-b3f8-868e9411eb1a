package it.yolo.client.product.response.dto;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "code",
        "description",
        "startDate",
        "recurring",
        "splits",
        "questions",
        "categories",
        "asset",
        "insuranceCompany"
})
public class Product {

    @JsonProperty("data")
    private DataProduct dataProduct;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    public DataProduct getDataProduct() {
        return dataProduct;
    }

    public void setDataProduct(DataProduct dataProduct) {
        this.dataProduct = dataProduct;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
