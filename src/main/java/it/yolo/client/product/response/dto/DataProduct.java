package it.yolo.client.product.response.dto;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "code",
        "description",
        "startDate",
        "recurring",
        "splits",
        "questions",
        "categories",
        "insuranceCompany",
        "price",
        "short_description",
        "images",
        "holder_maximum_age",
        "business",
        "display_price",
        "show_in_dashboard",
        "maximum_insurable",
        "only_contractor",
        "variants",
        "conditions_package",
        "packets",
        "insuranceCompanyLogo",
        "catalogId",
        "holder_minimum_age",
        "information_package",
        "can_open_claim",
        "conditions",
        "productDescription",
        "titleProd",
        "properties",
        "quotatorType"
})
public class DataProduct {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("code")
    private String code;
    @JsonProperty("description")
    private String description;
    @JsonProperty("startDate")
    private String startDate;
    @JsonProperty("recurring")
    private Boolean recurring;
    @JsonProperty("splits")
    private List<Object> splits = null;
    @JsonProperty("questions")
    private List<Question> questions = null;
    @JsonProperty("categories")
    private List<Category> categories = null;
    @JsonProperty("insuranceCompany")
    private String insuranceCompany;
    @JsonProperty("price")
    private String price;
    @JsonProperty("short_description")
    private String shortDescription;
    @JsonProperty("images")
    private ImagesProduct images;
    @JsonProperty("holder_maximum_age")
    private Integer holderMaximumAge;
    @JsonProperty("business")
    private Boolean business;
    @JsonProperty("display_price")
    private String displayPrice;
    @JsonProperty("show_in_dashboard")
    private Boolean showInDashboard;
    @JsonProperty("maximum_insurable")
    private Integer maximumInsurable;
    @JsonProperty("only_contractor")
    private Boolean onlyContractor;
    //    @JsonProperty("variants")
//    private Variants variants;
    @JsonProperty("conditions_package")
    private String conditionsPackage;
    @JsonProperty("packets")
    private List<Packet> packets = null;
    @JsonProperty("insuranceCompanyLogo")
    private String insuranceCompanyLogo;
    @JsonProperty("catalogId")
    private Integer catalogId;
    @JsonProperty("holder_minimum_age")
    private Integer holderMinimumAge;
    @JsonProperty("information_package")
    private String informationPackage;
    @JsonProperty("can_open_claim")
    private Boolean canOpenClaim;
    @JsonProperty("conditions")
    private String conditions;
    @JsonProperty("productDescription")
    private String productDescription;
    @JsonProperty("titleProd")
    private Object titleProd;
    @JsonProperty("planId")
    private String planId;
    @JsonProperty("planName")
    private String planName;
    //    @JsonProperty("properties")
//    private Properties properties;
    @JsonProperty("quotatorType")
    private String quotatorType;

    @JsonProperty("productsPaymentMethods")
    private List<ProductPaymentMethod> productPaymentMethod;

    @JsonProperty("legacy")
    private JsonNode legacy;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("code")
    public String getCode() {
        return code;
    }

    @JsonProperty("code")
    public void setCode(String code) {
        this.code = code;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("startDate")
    public String getStartDate() {
        return startDate;
    }

    @JsonProperty("startDate")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("recurring")
    public Boolean getRecurring() {
        return recurring;
    }

    @JsonProperty("recurring")
    public void setRecurring(Boolean recurring) {
        this.recurring = recurring;
    }

    @JsonProperty("splits")
    public List<Object> getSplits() {
        return splits;
    }

    @JsonProperty("splits")
    public void setSplits(List<Object> splits) {
        this.splits = splits;
    }

    @JsonProperty("questions")
    public List<Question> getQuestions() {
        return questions;
    }

    @JsonProperty("questions")
    public void setQuestions(List<Question> questions) {
        this.questions = questions;
    }

    @JsonProperty("categories")
    public List<Category> getCategories() {
        return categories;
    }

    @JsonProperty("categories")
    public void setCategories(List<Category> categories) {
        this.categories = categories;
    }

    @JsonProperty("insuranceCompany")
    public String getInsuranceCompany() {
        return insuranceCompany;
    }

    @JsonProperty("insuranceCompany")
    public void setInsuranceCompany(String insuranceCompany) {
        this.insuranceCompany = insuranceCompany;
    }

    @JsonProperty("price")
    public String getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(String price) {
        this.price = price;
    }

    @JsonProperty("short_description")
    public String getShortDescription() {
        return shortDescription;
    }

    @JsonProperty("short_description")
    public void setShortDescription(String shortDescription) {
        this.shortDescription = shortDescription;
    }

    @JsonProperty("images")
    public ImagesProduct getImages() {
        return images;
    }

    @JsonProperty("images")
    public void setImages(ImagesProduct images) {
        this.images = images;
    }

    @JsonProperty("holder_maximum_age")
    public Integer getHolderMaximumAge() {
        return holderMaximumAge;
    }

    @JsonProperty("holder_maximum_age")
    public void setHolderMaximumAge(Integer holderMaximumAge) {
        this.holderMaximumAge = holderMaximumAge;
    }

    @JsonProperty("business")
    public Boolean getBusiness() {
        return business;
    }

    @JsonProperty("business")
    public void setBusiness(Boolean business) {
        this.business = business;
    }

    @JsonProperty("display_price")
    public String getDisplayPrice() {
        return displayPrice;
    }

    @JsonProperty("display_price")
    public void setDisplayPrice(String displayPrice) {
        this.displayPrice = displayPrice;
    }

    @JsonProperty("show_in_dashboard")
    public Boolean getShowInDashboard() {
        return showInDashboard;
    }

    @JsonProperty("show_in_dashboard")
    public void setShowInDashboard(Boolean showInDashboard) {
        this.showInDashboard = showInDashboard;
    }

    @JsonProperty("maximum_insurable")
    public Integer getMaximumInsurable() {
        return maximumInsurable;
    }

    @JsonProperty("maximum_insurable")
    public void setMaximumInsurable(Integer maximumInsurable) {
        this.maximumInsurable = maximumInsurable;
    }

    @JsonProperty("only_contractor")
    public Boolean getOnlyContractor() {
        return onlyContractor;
    }

    @JsonProperty("only_contractor")
    public void setOnlyContractor(Boolean onlyContractor) {
        this.onlyContractor = onlyContractor;
    }

//    @JsonProperty("variants")
//    public Variants getVariants() {
//        return variants;
//    }
//
//    @JsonProperty("variants")
//    public void setVariants(Variants variants) {
//        this.variants = variants;
//    }

    @JsonProperty("conditions_package")
    public String getConditionsPackage() {
        return conditionsPackage;
    }

    @JsonProperty("conditions_package")
    public void setConditionsPackage(String conditionsPackage) {
        this.conditionsPackage = conditionsPackage;
    }

    @JsonProperty("packets")
    public List<Packet> getPackets() {
        return packets;
    }

    @JsonProperty("packets")
    public void setPackets(List<Packet> packets) {
        this.packets = packets;
    }

    @JsonProperty("insuranceCompanyLogo")
    public String getInsuranceCompanyLogo() {
        return insuranceCompanyLogo;
    }

    @JsonProperty("insuranceCompanyLogo")
    public void setInsuranceCompanyLogo(String insuranceCompanyLogo) {
        this.insuranceCompanyLogo = insuranceCompanyLogo;
    }

    @JsonProperty("catalogId")
    public Integer getCatalogId() {
        return catalogId;
    }

    @JsonProperty("catalogId")
    public void setCatalogId(Integer catalogId) {
        this.catalogId = catalogId;
    }

    @JsonProperty("holder_minimum_age")
    public Integer getHolderMinimumAge() {
        return holderMinimumAge;
    }

    @JsonProperty("holder_minimum_age")
    public void setHolderMinimumAge(Integer holderMinimumAge) {
        this.holderMinimumAge = holderMinimumAge;
    }

    @JsonProperty("information_package")
    public String getInformationPackage() {
        return informationPackage;
    }

    @JsonProperty("information_package")
    public void setInformationPackage(String informationPackage) {
        this.informationPackage = informationPackage;
    }

    @JsonProperty("can_open_claim")
    public Boolean getCanOpenClaim() {
        return canOpenClaim;
    }

    @JsonProperty("can_open_claim")
    public void setCanOpenClaim(Boolean canOpenClaim) {
        this.canOpenClaim = canOpenClaim;
    }

    @JsonProperty("conditions")
    public String getConditions() {
        return conditions;
    }

    @JsonProperty("conditions")
    public void setConditions(String conditions) {
        this.conditions = conditions;
    }

    @JsonProperty("productDescription")
    public String getProductDescription() {
        return productDescription;
    }

    @JsonProperty("productDescription")
    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    @JsonProperty("titleProd")
    public Object getTitleProd() {
        return titleProd;
    }

    @JsonProperty("titleProd")
    public void setTitleProd(Object titleProd) {
        this.titleProd = titleProd;
    }

//    @JsonProperty("properties")
//    public Properties getProperties() {
//        return properties;
//    }
//
//    @JsonProperty("properties")
//    public void setProperties(Properties properties) {
//        this.properties = properties;
//    }

    @JsonProperty("quotatorType")
    public String getQuotatorType() {
        return quotatorType;
    }

    @JsonProperty("quotatorType")
    public void setQuotatorType(String quotatorType) {
        this.quotatorType = quotatorType;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @JsonProperty("productsPaymentMethods")
    public List<ProductPaymentMethod> getProductPaymentMethod() {
        return productPaymentMethod;
    }
    @JsonProperty("productsPaymentMethods")
    public void setProductPaymentMethod(List<ProductPaymentMethod> productPaymentMethod) {
        this.productPaymentMethod = productPaymentMethod;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public void setAdditionalProperties(Map<String, Object> additionalProperties) {
        this.additionalProperties = additionalProperties;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public JsonNode getLegacy() {
        return legacy;
    }

    public void setLegacy(JsonNode legacy) {
        this.legacy = legacy;
    }
}