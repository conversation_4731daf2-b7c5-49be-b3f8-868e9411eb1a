package it.yolo.client.product.response.dto;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id",
        "image"
})
@Generated("jsonschema2pojo")
public class ImagesPacket {

    @JsonProperty("images")
    private List<ImagePacket> imagesPacket;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("images")
    public List<ImagePacket> getImage() {
        return imagesPacket;
    }

    @JsonProperty("images")
    public void setImage(List<ImagePacket> image) {
        this.imagesPacket = imagesPacket;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
