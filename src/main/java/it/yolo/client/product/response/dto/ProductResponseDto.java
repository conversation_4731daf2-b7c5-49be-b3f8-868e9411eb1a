package it.yolo.client.product.response.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductResponseDto {

    @JsonProperty("data")
    private DataProduct dataProduct;

    @JsonProperty("data")
    public DataProduct getDataProduct() {
        return dataProduct;
    }

    @JsonProperty("data")
    public void setDataProduct(DataProduct dataProduct) {
        this.dataProduct = dataProduct;
    }
}
